import {
  Avatar,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  Text,
  Tooltip,
  useToast,
  FormErrorMessage,
  InputRightElement,
  InputGroup,
  InputLeftElement,
  chakra,
  Box,
  SimpleGrid,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useBreakpointValue,
  Checkbox,
} from "@chakra-ui/react";

import React, { useEffect, useState, useRef } from "react";
import { FaEdit } from "react-icons/fa";
import { MdDelete } from "react-icons/md";
import ReactQuill from "react-quill";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { lookupPincodeLocation } from "../../utilities/googleMapsUtils";
import ReactDatePicker from "react-datepicker";
import { FaUserAlt, FaLock } from "react-icons/fa";
import "react-datepicker/dist/react-datepicker.css";
import "./datepicker.css";
import { useSelector } from "react-redux";
import { Country, State, City } from "country-state-city";
import { getGeoLocations } from "./getGeoLocation";
import toTime from "../../utilities/toTime";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Seo, { schema } from "../../utilities/Seo";

const deepEqual = (obj1, obj2) => {
  if (obj1 === obj2) return true;

  if (obj1 == null || obj2 == null) return obj1 === obj2;

  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return obj1 === obj2;

  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

  if (Array.isArray(obj1)) {
    if (obj1.length !== obj2.length) return false;
    for (let i = 0; i < obj1.length; i++) {
      if (!deepEqual(obj1[i], obj2[i])) return false;
    }
    return true;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (let key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

// Utility function to get only changed fields
const getChangedFields = (initialValues, currentValues) => {
  const changes = {};

  for (const key in currentValues) {
    if (!deepEqual(initialValues[key], currentValues[key])) {
      changes[key] = currentValues[key];
    }
  }

  return changes;
};

// Add custom styles for DatePicker positioning
const datePickerStyles = `
  .react-datepicker-popper {
    z-index: 9999 !important;
  }
  .react-datepicker {
    z-index: 9999 !important;
    position: absolute !important;
  }
  .react-datepicker__time-container {
    z-index: 9999 !important;
  }
  .react-datepicker__time-box {
    z-index: 9999 !important;
  }
  .react-datepicker__triangle {
    display: none !important;
  }
  .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
    display: none !important;
  }
  .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle {
    display: none !important;
  }
`;

// Inject styles
if (typeof navigator !== "undefined" && /Mac/i.test(navigator.userAgent)) {
  if (typeof document !== "undefined") {
    const styleSheet = document.createElement("style");
    styleSheet.type = "text/css";
    styleSheet.innerText = datePickerStyles;
    document.head.appendChild(styleSheet);
  }
}

const CFaUserAlt = chakra(FaUserAlt);
const CFaLock = chakra(FaLock);

const defaultImageUrl =
  "https://media.istockphoto.com/id/1300845620/vector/user-icon-flat-isolated-on-white-background-user-symbol-vector-illustration.jpg?s=612x612&w=0&k=20&c=yBeyba0hUkh14_jgv1OKqIH0CCSWU_4ckRkAoy2p73o=";

const BasicDetailsCoach = ({ coachData }) => {
  const [profileImagePreview, setProfileImagePreview] =
    useState(defaultImageUrl);
  const [isSubmitBtnLoading, setIsSubmitBtnLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [pincodeError, setPincodeError] = useState(false);
  const [shareError, setShareError] = useState("");
  const [academyFacilities, setAcademyFacilities] = useState([]);
  const [selectedFacilities, setSelectedFacilities] = useState([]);
  const [isDiscardDisabled, setIsDiscardDisabled] = useState(false);
  const [formChanged, setFormChanged] = useState(false);

  // Store initial values to track changes
  const initialValuesRef = useRef(null);


  // Safari detection function
  const isSafari = () => {
    const userAgent = navigator.userAgent;
    return /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  };

  const handleShowClick = () => setShowPassword(!showPassword);

  const toast = useToast();
  const navigate = useNavigate();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);
  const [states, setStates] = useState([]);

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
    fetchAcademyFacilities();
  }, []);

  const fetchAcademyFacilities = async () => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/academy/${userData.academyId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setAcademyFacilities(response.data?.data?.linkedFacilities || []);
    } catch (error) {
      console.error("Error fetching academy facilities:", error);
    }
  };

  const handleFacilitySelection = async (facility, isSelected) => {
    if (isSelected) {
      // Get coordinates for the facility
      try {
        const addressForGeocoding = `${facility.addressLine1}, ${facility.city}, ${facility.state}, ${facility.country}`.trim();
        const geoData = await getGeoLocations(addressForGeocoding);

        const facilityWithCoords = {
          ...facility,
          facilityType: 'academy',
          location: {
            type: "Point",
            coordinates: geoData?.data ? [geoData.data.Latitude, geoData.data.Longitude] : [],
            is_location_exact: geoData?.data ? true : false,
          },
        };

        setSelectedFacilities(prev => [...prev, facilityWithCoords]);

        // Clear ONLY empty manual facilities (not filled) to close the form
        if (formik.values.linkedFacilities.length > 0) {
          const emptyFacilities = formik.values.linkedFacilities.filter(facility =>
            !facility.name && !facility.addressLine1 && !facility.city && !facility.state && !facility.pinCode
          );

          const filledFacilities = formik.values.linkedFacilities.filter(facility =>
            facility.name || facility.addressLine1 || facility.city || facility.state || facility.pinCode
          );

          if (emptyFacilities.length > 0) {
            // Remove only empty facilities, keep filled ones
            formik.setFieldValue('linkedFacilities', filledFacilities);
          }
        }
      } catch (error) {
        const facilityWithCoords = {
          ...facility,
          facilityType: 'academy',
          location: {
            type: "Point",
            coordinates: [],
            is_location_exact: false,
          },
        };
        setSelectedFacilities(prev => [...prev, facilityWithCoords]);

        // Clear ONLY empty manual facilities (not filled) to close the form
        if (formik.values.linkedFacilities.length > 0) {
          const emptyFacilities = formik.values.linkedFacilities.filter(facility =>
            !facility.name && !facility.addressLine1 && !facility.city && !facility.state && !facility.pinCode
          );

          const filledFacilities = formik.values.linkedFacilities.filter(facility =>
            facility.name || facility.addressLine1 || facility.city || facility.state || facility.pinCode
          );

          if (emptyFacilities.length > 0) {
            // Remove only empty facilities, keep filled ones
            formik.setFieldValue('linkedFacilities', filledFacilities);
          }
        }
      }
    } else {
      setSelectedFacilities(prev => prev.filter(f => f._id !== facility._id));
    }
    if (formik.errors.facilities) {
      formik.setFieldError('facilities', undefined);
    }
    handleFormChange();
  };
  const handleSelectImagesClick = () => {
    document.getElementById("fileInput").click();
  };

  const calculateAge = async (dob) => {
    const currentDate = new Date();
    const dobDate = new Date(dob);
    let age = currentDate.getFullYear() - dobDate.getFullYear();
    if (
      currentDate.getMonth() < dobDate.getMonth() ||
      (currentDate.getMonth() === dobDate.getMonth() &&
        currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  const handleProfileImageChange = async (e) => {
    try {
      e.stopPropagation();
      const file = e.currentTarget.files[0];
      
      // Check if file exists
      if (!file) {
        e.target.value = null;
        return;
      }

      // Check file size
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB",
          status: "warning",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
        return;
      }

      // Check file format - only allow JPG, JPEG, and PNG
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileExtension = file.name.toLowerCase().split('.').pop();
      const allowedExtensions = ['jpg', 'jpeg', 'png'];
      
      // Debug logging
      console.log('File details:', {
        name: file.name,
        type: file.type,
        extension: fileExtension,
        size: file.size
      });
      
      // Strict validation - both MIME type and extension must match
      const isValidMimeType = allowedTypes.includes(file.type);
      const isValidExtension = allowedExtensions.includes(fileExtension);
      
      console.log('Validation results:', {
        isValidMimeType,
        isValidExtension,
        allowedTypes,
        allowedExtensions
      });
      
      if (!isValidMimeType || !isValidExtension) {
        console.log('File validation failed - rejecting upload');
        toast({
          title: "Invalid file format",
          description: "Please select only JPG, JPEG, or PNG files",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
        return;
      }
      
      console.log('File validation passed - proceeding with upload');
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      if (url) {
        toast({
          title: "Profile image uploaded",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      } else {
        toast({
          title: "Something went wrong while uploading profile image",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      }
      formik.setFieldValue("profileImg", url);
      setProfileImagePreview(url);
    } catch (error) {
      if (error.response?.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (url) => {
    if (!userData?.accessScopes?.coach?.includes("delete")) {
      toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const resp = response?.data;
        if (resp) {
          if (url) {
            toast({
              title: "Profile image deleted.",
              status: "success",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong while deleting profile image.",
              status: "error",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          }
        }
        formik.setFieldValue("profileImg", "");
        setProfileImagePreview(defaultImageUrl);
      } catch (error) {
        toast({
          title: "Something went wrong while deleting profile image.",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const phoneRegExp = /^[6-9]\d{9}$/;
  const passwordRegExp =
    /^(?=.*[A-Z])(?=.*[!@#$%^&*()_+|~=`{}[\]:";'<>?,./]).{8,}$/;
  const pincodeRegex = /^\d{6}$/;

  // Helper function to convert time string (HH:MM) to minutes
  const timeToMinutes = (timeString) => {
    if (!timeString) return 0;
    const [hours, minutes] = timeString.split(":").map(Number);
    return hours * 60 + minutes;
  };

  // Helper to convert minutes back to HH:MM string (24-hour)
  const minutesToTimeString = (totalMinutes) => {
    const hrs = Math.floor(((totalMinutes % (24 * 60)) + (24 * 60)) % (24 * 60) / 60);
    const mins = Math.floor(((totalMinutes % (24 * 60)) + (24 * 60)) % (24 * 60) % 60);
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // Function to scroll to first error field
  const scrollToError = (errors) => {
    // Flatten nested errors to get the first actual field with an error
    const getFirstErrorField = (obj, prefix = '') => {
      for (const key in obj) {
        const value = obj[key];
        const fullKey = prefix ? `${prefix}.${key}` : key;

        if (typeof value === 'string') {
          // This is an error message, return the field name
          return fullKey;
        } else if (typeof value === 'object' && value !== null) {
          // This is a nested object, recurse
          const nestedField = getFirstErrorField(value, fullKey);
          if (nestedField) return nestedField;
        }
      }
      return null;
    };

    const firstErrorField = getFirstErrorField(errors);

    if (firstErrorField) {
      let elementId = firstErrorField;

      // Handle nested fields like academyAvailability.endTime
      if (firstErrorField.includes(".")) {
        elementId = firstErrorField.replace(/\./g, "\\.");
      }

      const element =
        document.querySelector(`[name="${firstErrorField}"]`) ||
        document.getElementById(elementId) ||
        document.querySelector(`[id*="${firstErrorField}"]`);

      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
        // Add a highlight effect
        element.style.boxShadow = "0 0 0 3px rgba(239, 68, 68, 0.5)";
        setTimeout(() => {
          element.style.boxShadow = "";
        }, 3000);
      }
    }
  };

  // Track form changes
  const handleFormChange = () => {
    setFormChanged(true);
    setIsDiscardDisabled(false); // Re-enable discard when form changes
  };

 const formik = useFormik({
  initialValues: {
    firstName: coachData?.firstName || "",
    lastName: coachData?.lastName || "",
    gender: coachData?.gender || "",
    email: coachData?.email || "",
    mobile: coachData?.mobile || "",
    dob: coachData?.dob ? new Date(coachData.dob) : "",
    password: coachData?.email ? "***********" : "",
    profileImg: coachData?.profileImg || "",
    age: coachData?.age || "",
    alternateMobile: coachData?.alternateMobile || "",
    linkedFacilities: coachData?.linkedFacilities?.filter(f => f.type === 'coach') || [],
    affiliationType: coachData?.affiliationType || "academy",
    coachShare: coachData?.coachShare || "",
    academyShare: coachData?.academyShare || "",
    academyAvailability: coachData?.academyAvailability
      ? {
        startDate: coachData.academyAvailability.startDate
          ? coachData.academyAvailability.startDate.split("T")[0]
          : "",
        endDate: coachData.academyAvailability.endDate
          ? coachData.academyAvailability.endDate.split("T")[0]
          : "",
        startTime: coachData.academyAvailability.startTime || "",
        endTime: coachData.academyAvailability.endTime || "",
        days: Array.isArray(coachData.academyAvailability.days) ? coachData.academyAvailability.days : [],
      }
      : {
        startDate: "",
        endDate: "",
        startTime: "",
        endTime: "",
        days: [],
      },
  },
  validateOnChange: true,
  validateOnBlur: true,
  validateOnMount: false,

  // Simplified validation schema - only for new coaches
  validationSchema: !coachData ? Yup.object().shape({
    firstName: Yup.string()
      .required("First name is required")
      .min(3, "First name must be at least 3 characters")
      .max(50, "Firstname must be less than or equal to 50 characters")
      .test('no-spaces', 'First name cannot contain only spaces', function(value) {
        if (!value) return true; // Let required validation handle empty values
        return value.trim().length > 0;
      }),
    lastName: Yup.string()
      .required("Last name is required")
      .min(3, "Last name must be at least 3 characters")
      .max(50, "Last name must be less than or equal to 50 characters")
      .test('no-spaces', 'Last name cannot contain only spaces', function(value) {
        if (!value) return true; // Let required validation handle empty values
        return value.trim().length > 0;
      }),
    mobile: Yup.string()
      .matches(phoneRegExp, "Invalid mobile number")
      .required("Mobile number is required"),
    alternateMobile: Yup.string()
      .test('is-valid-mobile', 'Invalid mobile number', function (value) {
        if (!value || value.trim() === '') return true;
        return phoneRegExp.test(value);
      })
      .test('not-same-as-primary', 'Alternate number cannot be same as primary', function (value) {
        if (!value || value.trim() === '') return true;
        return value !== this.parent.mobile;
      }),
    gender: Yup.string().required("Gender is required"),
    coachShare: Yup.number()
      .typeError("Share must be a number")
      .required("Coach share is required")
      .min(0)
      .max(100),
    academyShare: Yup.number()
      .typeError("Share must be a number")
      .required("Academy share is required")
      .min(0)
      .max(100)
      .test("shares-total", "Coach + Academy share cannot exceed 100", function (values) {
        const { coachShare, academyShare } = values;
        return (coachShare || 0) + (academyShare || 0) <= 100;
      }),
    linkedFacilities: Yup.array().of(
      Yup.object().shape({
        name: Yup.string()
          .test('required-if-any-field', 'Facility name is required', function (value) {
            const facility = this.parent;
            const hasAnyField = facility.addressLine1 || facility.city || facility.state || facility.pinCode || facility.country;
            if (hasAnyField && (!value || value.trim() === '')) {
              return false;
            }
            return true;
          })
          .min(3, "Facility name must be at least 3 characters")
          .max(100, "Facility name must be less than or equal to 100 characters"),
        country: Yup.string()
          .test('required-if-any-field', 'Country is required', function (value) {
            const facility = this.parent;
            const hasAnyField = facility.name || facility.addressLine1 || facility.city || facility.state || facility.pinCode;
            if (hasAnyField && (!value || value.trim() === '')) {
              return false;
            }
            return true;
          })
          .max(100, "Country name must be less than or equal to 100 characters"),
        addressLine1: Yup.string()
          .test('required-if-any-field', 'Address Line 1 is required', function (value) {
            const facility = this.parent;
            const hasAnyField = facility.name || facility.city || facility.state || facility.pinCode || facility.country;
            if (hasAnyField && (!value || value.trim() === '')) {
              return false;
            }
            return true;
          })
          .min(3, "Address Line 1 must be at least 3 characters")
          .max(100, "Address Line 1 must be less than or equal to 100 characters"),
        city: Yup.string()
          .test('required-if-any-field', 'City is required', function (value) {
            const facility = this.parent;
            const hasAnyField = facility.name || facility.addressLine1 || facility.state || facility.pinCode || facility.country;
            if (hasAnyField && (!value || value.trim() === '')) {
              return false;
            }
            return true;
          })
          .max(100, "City name must be less than or equal to 100 characters"),
        state: Yup.string()
          .test('required-if-any-field', 'State is required', function (value) {
            const facility = this.parent;
            const hasAnyField = facility.name || facility.addressLine1 || facility.city || facility.pinCode || facility.country;
            if (hasAnyField && (!value || value.trim() === '')) {
              return false;
            }
            return true;
          })
          .max(100, "State name must be less than or equal to 100 characters"),
        pinCode: Yup.string()
          .test('required-if-any-field', 'Pincode is required', function (value) {
            const facility = this.parent;
            const hasAnyField = facility.name || facility.addressLine1 || facility.city || facility.state || facility.country;
            if (hasAnyField && (!value || value.trim() === '')) {
              return false;
            }
            return true;
          })
          .matches(pincodeRegex, "Invalid pin code"),
      })
    ),
    profileImg: Yup.string().nullable().required("Profile image is required"),
    // For new coaches only
    ...(!coachData && {
      email: Yup.string()
        .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, "Invalid email")
        .required("Email is required"),
      password: Yup.string()
        .required("Password is required")
        .matches(passwordRegExp, "Password must contain at least one capital letter and one special character"),
      dob: Yup.date()
        .required("Date of birth is required")
        .max(new Date(new Date().setFullYear(new Date().getFullYear() - 18)), "Must be at least 18 years old"),
      academyAvailability: Yup.object().shape({
        startDate: Yup.string()
          .required("Start date is required")
          .test("not-in-past", "Start date cannot be in the past", function (value) {
            if (!value) return true;
            const today = new Date().toISOString().split("T")[0];
            return value >= today;
          }),
        endDate: Yup.string()
          .required("End date is required")
          .test("not-in-past", "End date cannot be in the past", function (value) {
            if (!value) return true;
            const today = new Date().toISOString().split("T")[0];
            return value >= today;
          })
          .test("is-after-start-date", "End date must be after start date", function (value) {
            const { startDate } = this.parent;
            if (!startDate) return true;
            if (!value) {
              const tenYearsLater = new Date(startDate);
              tenYearsLater.setFullYear(tenYearsLater.getFullYear() + 10);
              this.parent.endDate = tenYearsLater.toISOString().split("T")[0];
              return true;
            }
            return value > startDate;
          }),
        startTime: Yup.string().required("Start time is required"),
        endTime: Yup.string()
          .required("End time is required")
          .test("is-after-start-time", "End time must be at least 30 minutes after start time", function (value) {
            const { startTime } = this.parent;
            if (!startTime || !value) return true;
            const startMinutes = timeToMinutes(startTime);
            const endMinutes = timeToMinutes(value);
            return endMinutes >= startMinutes + 30;
          }),
        days: Yup.array().min(1, "Select at least one day").required("Select Days is required"),
      }),
    }),
  }) : undefined,
  validate: (values) => {
    const errors = {};

    // Validate firstName and lastName for both new and existing coaches
    if (values.firstName) {
      const trimmedFirstName = values.firstName.trim();
      if (trimmedFirstName.length === 0) {
        errors.firstName = "First name cannot contain only spaces";
      } else if (trimmedFirstName.length < 3) {
        errors.firstName = "First name must be at least 3 characters";
      } else if (values.firstName.length > 50) {
        errors.firstName = "First name must be less than or equal to 50 characters";
      }
    }

    if (values.lastName) {
      const trimmedLastName = values.lastName.trim();
      if (trimmedLastName.length === 0) {
        errors.lastName = "Last name cannot contain only spaces";
      } else if (trimmedLastName.length < 3) {
        errors.lastName = "Last name must be at least 3 characters";
      } else if (values.lastName.length > 50) {
        errors.lastName = "Last name must be less than or equal to 50 characters";
      }
    }

    // For new coaches only - validate at least one facility
    if (!coachData) {
      const hasSelectedFacilities = selectedFacilities.length > 0;
      const hasValidManualFacilities = values.linkedFacilities.some(f =>
        f.name && f.addressLine1 && f.city && f.state && f.pinCode
      );

      if (!hasSelectedFacilities && !hasValidManualFacilities) {
        errors.facilities = "At least one facility is required (either select from academy facilities or add manually)";
      }
    }

    // For existing coaches - skip academy availability validation entirely unless touched
    if (coachData) {
      // Don't validate academy availability at all unless user has touched those fields
      // This prevents unwanted validation errors when updating other fields like name
    }

    return errors;
  },

  onSubmit: async (values) => {
    if (coachData) {
      // First, clear any academy availability errors if fields haven't changed
      // This prevents old errors from blocking the update
      const initialAvailability = initialValuesRef.current?.academyAvailability;
      const currentAvailability = values.academyAvailability;
      
      const hasAcademyAvailabilityChanged =
        initialAvailability?.startDate !== currentAvailability?.startDate ||
        initialAvailability?.endDate !== currentAvailability?.endDate ||
        initialAvailability?.startTime !== currentAvailability?.startTime ||
        initialAvailability?.endTime !== currentAvailability?.endTime ||
        JSON.stringify(initialAvailability?.days) !== JSON.stringify(currentAvailability?.days);

      // Clear academy availability errors if nothing changed
      if (!hasAcademyAvailabilityChanged) {
        formik.setFieldError('academyAvailability.startDate', undefined);
        formik.setFieldError('academyAvailability.endDate', undefined);
        formik.setFieldError('academyAvailability.startTime', undefined);
        formik.setFieldError('academyAvailability.endTime', undefined);
        formik.setFieldError('academyAvailability.days', undefined);
      }

      const basicFieldErrors = {};
      
      // Validate basic fields
      if (!values.firstName || values.firstName.trim().length < 3) {
        basicFieldErrors.firstName = "First name must be at least 3 characters";
      } else if (values.firstName.trim().length === 0) {
        basicFieldErrors.firstName = "First name cannot contain only spaces";
      }
      if (!values.lastName || values.lastName.trim().length < 3) {
        basicFieldErrors.lastName = "Last name must be at least 3 characters";
      } else if (values.lastName.trim().length === 0) {
        basicFieldErrors.lastName = "Last name cannot contain only spaces";
      }
      if (!values.mobile || !phoneRegExp.test(values.mobile)) {
        basicFieldErrors.mobile = "Invalid mobile number";
      }
      if (values.alternateMobile && !phoneRegExp.test(values.alternateMobile)) {
        basicFieldErrors.alternateMobile = "Invalid mobile number";
      }
      if (values.alternateMobile && values.alternateMobile === values.mobile) {
        basicFieldErrors.alternateMobile = "Alternate number cannot be same as primary";
      }
      if (!values.gender) {
        basicFieldErrors.gender = "Gender is required";
      }
      if (values.coachShare == null || values.coachShare === "" || values.coachShare < 0 || values.coachShare > 100) {
        basicFieldErrors.coachShare = "Coach share must be between 0 and 100";
      }
      if (values.academyShare == null || values.academyShare === "" || values.academyShare < 0 || values.academyShare > 100) {
        basicFieldErrors.academyShare = "Academy share must be between 0 and 100";
      }
      if ((values.coachShare || 0) + (values.academyShare || 0) > 100) {
        basicFieldErrors.academyShare = "Coach + Academy share cannot exceed 100";
      }
      if (!values.profileImg) {
        basicFieldErrors.profileImg = "Profile image is required";
      }

      // If there are basic field errors, show them and return
      if (Object.keys(basicFieldErrors).length > 0) {
        Object.keys(basicFieldErrors).forEach(field => {
          formik.setFieldError(field, basicFieldErrors[field]);
        });
        setIsSubmitBtnLoading(false);
        return;
      }

      // Check if any academy availability fields have been touched by the user
      const hasAcademyAvailabilityBeenTouched = 
        formik.touched.academyAvailability?.startDate ||
        formik.touched.academyAvailability?.endDate ||
        formik.touched.academyAvailability?.startTime ||
        formik.touched.academyAvailability?.endTime ||
        formik.touched.academyAvailability?.days;

      // Only validate academy availability if fields have actually changed from initial values
      // This allows updating other fields without being blocked by past dates
      if (hasAcademyAvailabilityChanged) {
        const availability = values.academyAvailability || {};
        const availabilityErrors = {};

        if (!availability.startDate) {
          availabilityErrors.startDate = "Start date is required";
        } else {
          // Only validate start date is not in past if it has been changed
          const initialStartDate = initialValuesRef.current?.academyAvailability?.startDate;
          const currentStartDate = availability.startDate;

          // Only validate past date if the start date field was actually changed by the user
          if (initialStartDate !== currentStartDate) {
            const today = new Date().toISOString().split("T")[0];
            if (currentStartDate < today) {
              availabilityErrors.startDate = "Start date cannot be in the past";
            }
          } 
        }

        if (!availability.endDate) {
          availabilityErrors.endDate = "End date is required";
        } else if (availability.startDate && availability.endDate <= availability.startDate) {
          availabilityErrors.endDate = "End date must be after start date";
        }

        if (!availability.startTime) {
          availabilityErrors.startTime = "Start time is required";
        }

        if (!availability.endTime) {
          availabilityErrors.endTime = "End time is required";
        } else if (availability.startTime && availability.endTime) {
          const startMinutes = timeToMinutes(availability.startTime);
          const endMinutes = timeToMinutes(availability.endTime);
          if (endMinutes < startMinutes + 30) {
            availabilityErrors.endTime = "End time must be at least 30 minutes after start time";
          }
        }

        if (!availability.days || availability.days.length === 0) {
          availabilityErrors.days = "Select at least one day";
        }

        if (Object.keys(availabilityErrors).length > 0) {

          // Mark all academy availability fields as touched to show errors FIRST
          formik.setTouched({
            ...formik.touched,
            academyAvailability: {
              startTime: true,
              endTime: true,
              startDate: true,
              endDate: true,
              days: true,
            }
          });

          // Then set the errors
          Object.keys(availabilityErrors).forEach(field => {
            formik.setFieldError(`academyAvailability.${field}`, availabilityErrors[field]);
          });

          // Scroll to the first error after state updates (increased timeout)
          setTimeout(() => {
            scrollToError({ academyAvailability: availabilityErrors });
          }, 200);

          setIsSubmitBtnLoading(false);
          return;
        } 
      } 
    } 

    setShareError("");
    setIsSubmitBtnLoading(true);
    
    const age = await calculateAge(values.dob);
    await formik.setFieldValue("age", age);
    await formik.setFieldValue("profileImg", `${profileImagePreview}`);

    if (coachData) {
      delete values.email;
      delete values.password;
    }

    if (pincodeError) {
      toast({
        title: "Pincode is not correct",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      setIsSubmitBtnLoading(false);
      return;
    }

    // Process academyAvailability - only for new coaches or when academy availability has changed
    let formattedAcademyAvailability = null;
    let hasAcademyAvailabilityChanged = false;

    if (!coachData) {
      // For new coaches, always process academy availability
      hasAcademyAvailabilityChanged = true;
    } else {
      // For existing coaches, only process if academy availability fields have been touched and changed
      const hasAcademyAvailabilityBeenTouched = 
        formik.touched.academyAvailability?.startDate ||
        formik.touched.academyAvailability?.endDate ||
        formik.touched.academyAvailability?.startTime ||
        formik.touched.academyAvailability?.endTime ||
        formik.touched.academyAvailability?.days;

      const originalAvailability = coachData.academyAvailability || {};
      const currentAvailability = values.academyAvailability || {};

      const originalStartDate = originalAvailability.startDate ? originalAvailability.startDate.split('T')[0] : '';
      const currentStartDate = currentAvailability.startDate || '';

      const originalEndDate = originalAvailability.endDate ? originalAvailability.endDate.split('T')[0] : '';
      const currentEndDate = currentAvailability.endDate || '';

      hasAcademyAvailabilityChanged =
        originalStartDate !== currentStartDate ||
        originalEndDate !== currentEndDate ||
        (originalAvailability.startTime || '') !== (currentAvailability.startTime || '') ||
        (originalAvailability.endTime || '') !== (currentAvailability.endTime || '') ||
        JSON.stringify(originalAvailability.days || []) !== JSON.stringify(currentAvailability.days || []);

    }

    // Only format and process academyAvailability if it has changed (or for new coaches)
    if (hasAcademyAvailabilityChanged) {
      formattedAcademyAvailability = { ...values.academyAvailability };
      if (formattedAcademyAvailability.startDate) {
        formattedAcademyAvailability.startDate = new Date(
          formattedAcademyAvailability.startDate
        ).toISOString();
      }
      if (formattedAcademyAvailability.endDate) {
        formattedAcademyAvailability.endDate = new Date(
          formattedAcademyAvailability.endDate
        ).toISOString();
      } else if (formattedAcademyAvailability.startDate) {
        // If no end date is provided but start date exists, set it to 10 years from now
        formattedAcademyAvailability.endDate = getTenYearsLaterDate();
      }
      if (formattedAcademyAvailability.startTime) {
        formattedAcademyAvailability.startTime =
          formattedAcademyAvailability.startTime.slice(0, 5);
      }
      if (formattedAcademyAvailability.endTime) {
        formattedAcademyAvailability.endTime =
          formattedAcademyAvailability.endTime.slice(0, 5);
      }
    }

    // Process manual facilities
    const manualFacilities = await Promise.all(
      values.linkedFacilities.map(async (facility) => {
        let processedFacility = { ...facility, facilityType: 'manual' };

        // If coordinates are missing or empty, try to fetch them
        if (
          !processedFacility.location ||
          !processedFacility.location.coordinates ||
          processedFacility.location.coordinates.length === 0
        ) {
          try {
            const addressForGeocoding = `${facility.name || ""} ${facility.addressLine1 || ""
              } ${facility.city || ""} ${facility.state || ""} ${facility.country || ""
              }`.trim();
            if (
              addressForGeocoding.length > 0 &&
              facility.city &&
              facility.state
            ) {
              const geoData = await getGeoLocations(addressForGeocoding);
              if (geoData && geoData.data) {
                processedFacility.location = {
                  type: "Point",
                  coordinates: [
                    geoData.data.Latitude,
                    geoData.data.Longitude,
                  ],
                  is_location_exact: true,
                };
              }
            }
          } catch (error) {
            if (!processedFacility.location) {
              processedFacility.location = {
                type: "Point",
                coordinates: [],
                is_location_exact: false,
              };
            }
          }
        }

        return processedFacility;
      })
    );

    // Extract only IDs from selected academy facilities
    const academyFacilityIds = selectedFacilities.map(facility => facility._id);

    const processedLinkedFacilities = manualFacilities;

    let config;

    if (coachData) {
      // For updates, only send changed fields
      const currentValues = {
        ...values,
        linkedFacilities: processedLinkedFacilities,
        academyFacilities: academyFacilityIds,
        age: age,
        affiliationType: "academy",
        academyId: userData.academyId,
        coachShare: values.coachShare,
        academyShare: values.academyShare,
      };

      // Remove academyAvailability from currentValues to ensure clean comparison
      delete currentValues.academyAvailability;

      // Only add academyAvailability to currentValues if it has changed
      if (hasAcademyAvailabilityChanged && formattedAcademyAvailability !== null) {
        currentValues.academyAvailability = formattedAcademyAvailability;
      } 

      // Create a copy of initialValues without academyAvailability for comparison
      const initialValuesForComparison = initialValuesRef.current ? { ...initialValuesRef.current } : {};
      if (!hasAcademyAvailabilityChanged) {
        delete initialValuesForComparison.academyAvailability;
      }

      // Get only changed fields
      const changedFields = initialValuesRef.current
        ? getChangedFields(initialValuesForComparison, currentValues)
        : currentValues;

      // Always include certain fields that might be processed differently
      const fieldsToAlwaysInclude = ['linkedFacilities', 'academyFacilities'];
      fieldsToAlwaysInclude.forEach(field => {
        if (currentValues[field] !== undefined) {
          changedFields[field] = currentValues[field];
        }
      });

      // Ensure we have at least some data to send
      if (Object.keys(changedFields).length === 0) {
        toast({
          title: "No changes detected",
          status: "info",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
        setIsSubmitBtnLoading(false);
        return;
      }

      config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach/${coachData._id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: JSON.stringify(changedFields),
      };
    } else {
      // For new coach creation, send all fields
      config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: JSON.stringify({
          ...values,
          linkedFacilities: processedLinkedFacilities,
          academyFacilities: academyFacilityIds,
          age: age,
          affiliationType: "academy",
          academyId: userData.academyId,
          coachShare: values.coachShare,
          academyShare: values.academyShare,
          ...(formattedAcademyAvailability && { academyAvailability: formattedAcademyAvailability }),
        }),
      };
    }

    axios
      .request(config)
      .then((response) => {
        toast({
          title: coachData
            ? "Coach Profile Updated"
            : "Coach Profile Created",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        setIsSubmitBtnLoading(false);
        if (!coachData) {
          navigate(`/coach-page/details/${response?.data?.data?._id}`);
        }
      })
      .catch((error) => {
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          setIsSubmitBtnLoading(false);
        } else if (error.response.data.error) {
          toast({
            title: error.response.data.error,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          setIsSubmitBtnLoading(false);
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          setIsSubmitBtnLoading(false);
        }
      });
  },
});

  // Store initial values for change tracking
  useEffect(() => {
    if (formik.values && !initialValuesRef.current) {
      initialValuesRef.current = JSON.parse(JSON.stringify(formik.values));
    }
  }, [formik.values]);

  useEffect(() => {
    if (formik.errors.facilities) {
      const hasSelectedFacilities = selectedFacilities.length > 0;
      const hasValidManualFacilities = formik.values.linkedFacilities.some(f =>
        f.name && f.addressLine1 && f.city && f.state && f.pinCode
      );

      if (hasSelectedFacilities || hasValidManualFacilities) {
        formik.setFieldError('facilities', undefined);
      }
    }
  }, [selectedFacilities, formik.values.linkedFacilities, formik.errors.facilities]);

  const addAddress = () => {
    formik.setValues({
      ...formik.values,
      linkedFacilities: [
        ...formik.values.linkedFacilities,
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          // phone: "",
          amenities: "",
          location: {
            type: "Point",
            coordinates: [],
            is_location_exact: true,
          },
        },
      ],
    });

    if (formik.errors.facilities) {
      formik.setFieldError('facilities', undefined);
    }
  };

  const getDetailsFromPincode = async (pincode, index) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setPincodeError(true);
        return;
      }

      // Note: pincode value is already set in the onChange handler

      setPincodeError(false);

      // Use the utility function for pincode lookup
      const locationData = await lookupPincodeLocation(pincode, {
        statesArray: [], // Not using state mapping for coach
        useCountryCode: false,
        useStateIsoCode: false
      });

      formik.setFieldValue(`linkedFacilities[${index}].city`, locationData.city);
      formik.setFieldValue(`linkedFacilities[${index}].state`, locationData.state);
      formik.setFieldValue(`linkedFacilities[${index}].country`, locationData.country);

      // Get current facility data to build address for geocoding
      const currentFacility = formik.values.linkedFacilities[index];
      const facilityName = currentFacility.name || "";
      const addressLine1 = currentFacility.addressLine1 || "";

      // Fetch coordinates using getGeoLocations
      try {
        // Create a more specific address for better geocoding results
        const addressForGeocoding =
          `${addressLine1}, ${locationData.city}, ${locationData.state}, ${locationData.country}`.trim();

        if (addressForGeocoding.length > 0 && locationData.city && locationData.state) {
          const geoData = await getGeoLocations(addressForGeocoding);

          if (
            geoData &&
            geoData.data &&
            geoData.data.Latitude &&
            geoData.data.Longitude
          ) {
            const newCoordinates = [
              geoData.data.Latitude,
              geoData.data.Longitude,
            ];

            // Get current coordinates to compare
            const currentLocation = currentFacility.location;
            const currentCoordinates = currentLocation?.coordinates || [];
            
            // Check if coordinates have actually changed
            const coordinatesChanged = 
              currentCoordinates.length !== newCoordinates.length ||
              currentCoordinates[0] !== newCoordinates[0] ||
              currentCoordinates[1] !== newCoordinates[1];

            formik.setFieldValue(`linkedFacilities[${index}].location`, {
              type: "Point",
              coordinates: newCoordinates,
              is_location_exact: true,
            });

            // Only show success toast if coordinates actually changed
            if (coordinatesChanged) {
              toast({
                title: "Location coordinates updated",
                description: `Coordinates found for ${facilityName || "facility"}`,
                status: "success",
                duration: 2000,
                position: "top",
                isClosable: true,
              });
            }
          } else {
            formik.setFieldValue(`linkedFacilities[${index}].location`, {
              type: "Point",
              coordinates: [],
              is_location_exact: false,
            });
          }
        }
      } catch (geoError) {
        // Set default location structure even if geocoding fails
        formik.setFieldValue(`linkedFacilities[${index}].location`, {
          type: "Point",
          coordinates: [],
          is_location_exact: false,
        });

        // Show error toast
        toast({
          title: "Could not fetch location coordinates",
          description: "Please check the address details",
          status: "warning",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      setPincodeError(true);

      // Clear fields on error
      formik.setFieldValue(`linkedFacilities[${index}].city`, "");
      formik.setFieldValue(`linkedFacilities[${index}].state`, "");
      formik.setFieldValue(`linkedFacilities[${index}].country`, "");

      // Set error on the pincode field
      formik.setFieldError(
        `linkedFacilities[${index}].pinCode`,
        "Error fetching location details. Please try again."
      );

      // Handle different types of errors
      let errorMessage = "Please check your internet connection and try again";

      if (error.message.includes('Invalid pincode format')) {
        errorMessage = "Please enter a valid 6-digit pincode.";
      } else if (error.message.includes('API key not configured')) {
        errorMessage = "Configuration error. Please contact support.";
      } else if (error.message.includes('Invalid pincode or no results found')) {
        errorMessage = "The pincode you entered is not valid. Please check and try again.";
      }

      // Show error toast
      toast({
        title: "Error fetching location details",
        description: errorMessage,
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  // Function to update coordinates when facility details change
  const updateFacilityCoordinates = async (index) => {
    try {
      const facility = formik.values.linkedFacilities[index];
      if (!facility.city || !facility.state || !facility.addressLine1) {
        return;
      }
      const addressForGeocoding = `${facility.addressLine1}, ${facility.city
        }, ${facility.state}, ${facility.country || "India"}`.trim();

      const geoData = await getGeoLocations(addressForGeocoding);

      if (
        geoData &&
        geoData.data &&
        geoData.data.Latitude &&
        geoData.data.Longitude
      ) {
        const newCoordinates = [geoData.data.Latitude, geoData.data.Longitude];
        
        // Get current coordinates to compare
        const currentLocation = facility.location;
        const currentCoordinates = currentLocation?.coordinates || [];
        
        // Check if coordinates have actually changed
        const coordinatesChanged = 
          currentCoordinates.length !== newCoordinates.length ||
          currentCoordinates[0] !== newCoordinates[0] ||
          currentCoordinates[1] !== newCoordinates[1];

        formik.setFieldValue(`linkedFacilities[${index}].location`, {
          type: "Point",
          coordinates: newCoordinates,
          is_location_exact: true,
        });

        // Only show success toast if coordinates actually changed
        if (coordinatesChanged) {
          toast({
            title: "Coordinates updated",
            description: `Location updated for ${facility.name || "facility"}`,
            status: "success",
            duration: 2000,
            position: "top",
            isClosable: true,
          });
        }
      }
    } catch (error) {
      toast({
        title: "Could not update coordinates",
        description: "Please check the address details",
        status: "warning",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  // Custom TimeSelect using Menu/MenuItem (compact, lazy-mounted to avoid lag)
  const TimeSelect = React.memo(function TimeSelect({ value, onChange, onBlur, placeholder, disabled, name, id, isInvalid, selectedDate, minTime }) {
    const times = React.useMemo(() => {
      const list = [];
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const val = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour >= 12 ? 'PM' : 'AM';
          const label = `${hour12}:${minute.toString().padStart(2, '0')}${ampm}`;
          list.push({ value: val, label });
        }
      }

      // If a selectedDate is provided and it equals today's date, filter out times that have already passed
      try {
        if (selectedDate) {
          const todayStr = new Date().toISOString().split('T')[0];
          if (selectedDate === todayStr) {
            const now = new Date();
            // round up to next 15-minute slot
            const minutes = now.getMinutes();
            const rounded = Math.ceil(minutes / 15) * 15;
            let hour = now.getHours();
            let minute = rounded;
            if (minute === 60) {
              hour = (hour + 1) % 24;
              minute = 0;
            }
            const threshold = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            // apply minTime as well if provided
            if (minTime) {
              const earliest = threshold > minTime ? threshold : minTime;
              return list.filter((t) => t.value >= earliest);
            }
            return list.filter((t) => t.value >= threshold);
          }
        }
      } catch (e) {
        // if anything goes wrong, fall back to full list
        return list;
      }
      // If selectedDate isn't today, but minTime is provided, apply it
      if (minTime) {
        return list.filter((t) => t.value >= minTime);
      }

      return list;
  }, [selectedDate, minTime]);

    const selectedLabel = React.useMemo(() => {
      const found = times.find((t) => t.value === value);
      return found ? found.label : (placeholder || 'Select time');
    }, [times, value, placeholder]);

    return (
      <Menu isLazy lazyBehavior="unmount" closeOnSelect >
        <MenuButton
          as={Button}
          rightIcon={<chakra.span>▾</chakra.span>}
          variant="outline"
          width="100%"
          height="36px"
          fontSize="sm"
          textAlign="left"
          justifyContent="space-between"
          borderColor={isInvalid ? 'red.300' : 'gray.200'}
          _hover={{ borderColor: isInvalid ? 'red.400' : 'gray.300' }}
          disabled={disabled}
          bg={disabled ? 'gray.50' : 'white'}
          px={3}
          id={id}
        >
          <Text noOfLines={1} fontSize="sm">{selectedLabel}</Text>
        </MenuButton>
        <MenuList maxHeight="220px" overflowY="auto" zIndex={1200} p={1} fontSize="sm">
          {times.map((t) => (
            <MenuItem
              key={t.value}
              minH="28px"
              fontSize="sm"
              py={1.5}
              onClick={() => {
                // Set value first
                if (onChange) onChange({ target: { name, value: t.value } });
                // Then trigger blur to mark touched and re-run validation so required errors clear
                if (onBlur) {
                  if (typeof window !== 'undefined' && typeof window.requestAnimationFrame === 'function') {
                    window.requestAnimationFrame(() => onBlur({ target: { name } }));
                  } else {
                    setTimeout(() => onBlur({ target: { name } }), 0);
                  }
                }
              }}
            >
              {t.label}
            </MenuItem>
          ))}
        </MenuList>
      </Menu>
    );
  });
  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  function buildPayload(values) {
    const selectedAcademyFacilities = values?.academyLinkedFacilities || [];
    const affiliationType = values.accountType === "Joining via Academy" ? "academy" : "individual";

    // Academy info
    // let academyName = "";
    let academyId = "";
    if (affiliationType === "academy") {
      // academyName = values.academy.name || "";
      academyId = values.academyId;
    }

    // Only build academyAvailability if needed
    let academyAvailability;
    if (affiliationType === "academy") {
      let startDate = values.academyStartDate ? values.academyStartDate.replace(/\//g, "-") : "";
      let endDate = "";
      if (values.academyEndDate) {
        endDate = values.academyEndDate.replace(/\//g, "-");
      } else if (startDate) {
        // Add 10 years to the startDate
        const [year, month, day] = startDate.split("-");
        endDate = `${parseInt(year, 10) + 10}-${month}-${day}`;
      }
      academyAvailability = {
        startDate,
        endDate,
        days: (values.selectedDays || []).map(day => typeof day === 'string' ? day : day.value),
        startTime: values.academyStartTime || "",
        endTime: values.academyEndTime || "",
      };
    }

    // Base payload
    const payload = {
      firstName: values.firstName,
      lastName: values.lastName,
      gender: values.gender,
      email: values.email,
      mobile: Number(values.mobile),
      dob: values.dob ? values.dob.split("T")[0] : "",
      password: values.password,
      confirmPassword: values.confirmPassword,
      profileImg: values.profileImg,
      age: Number(values.age),
      alternateMobile: values.alternateMobile,
      linkedFacilities: [
        // Manual facilities - filter out empty ones
        ...(values.linkedFacilities || [])
          .filter(fac =>
            fac?.name &&
            fac?.addressLine1 &&
            fac?.city &&
            fac?.state &&
            fac?.pinCode
          )
          .map(fac => ({
            name: fac.name,
            addressLine1: fac.addressLine1,
            addressLine2: fac.addressLine2 || '',
            city: fac.city,
            state: fac.state,
            pinCode: fac.pinCode,
            country: fac.country || '',
            amenities: fac.amenities || [],
            facilityType: 'manual'
          })),
        // Academy facilities - include as-is
        ...selectedAcademyFacilities
      ].filter(Boolean), // Filter out any null/undefined entries
      experience: Number(values.experience),
      language: values.language,
      sportsCategories: values.sportsCategories,
      privacyPolicyAccepted: values.privacyPolicyAccepted,
      coachingQualifications: values.coachingQualifications || [],
      coachingExperience: values.coachingExperience || [],
      playerExperience: values.playerExperience || [],
      award: values.award || [],
      kycDocuments: {
        documentName: values.kycDocuments.documentName || "",
        documentNumber: values.kycDocuments.documentNumber,
        documentImg: (values.kycDocuments.documentImg || []).map(img => ({ url: img.url })),
      },
      aadhaarNumber: values.aadhaarNumber,
      aadhaarImage: values.aadhaarImage || [],
      bankDetails: {
        accountNumber: Number(values.bankDetails.accountNumber),
        accountHolderName: values.bankDetails.accountHolderName,
        ifsc: values.bankDetails.ifsc,
      },
      hasGst: values.hasGst,
      gstNumber: values.gstNumber,
      gstState: values.gstState,
      affiliationType,
    };

    // Only add academy fields if affiliationType is 'academy'
    if (affiliationType === "academy") {
      payload.academyId = academyId;
      payload.coachShare = Number(values.coachShare);
      payload.academyShare = Number(values.academyShare);
      payload.academyAvailability = academyAvailability;
    }

    return payload;
  }
  // Ensure academyAvailability is always properly initialized
  useEffect(() => {
    if (formik.values && !formik.values.academyAvailability) {
      formik.setFieldValue('academyAvailability', {
        startDate: "",
        endDate: "",
        startTime: "",
        endTime: "",
        days: [],
      });
    } else if (formik.values.academyAvailability && !Array.isArray(formik.values.academyAvailability.days)) {
      formik.setFieldValue('academyAvailability.days', []);
    }
  }, [formik.values.academyAvailability]);

  useEffect(() => {
    if (coachData && academyFacilities.length > 0) {
      setProfileImagePreview(coachData?.profileImg || defaultImageUrl);

      // Find academy facilities that match coach's linked facilities
      const matchedFacilities = [];

      coachData.linkedFacilities?.forEach(coachFacility => {
        if (coachFacility.type === 'academy') {
          const matchingAcademyFacility = academyFacilities.find(academyFacility =>
            academyFacility._id === coachFacility.facilityId ||
            (academyFacility.name === coachFacility.name &&
              academyFacility.addressLine1 === coachFacility.addressLine1 &&
              academyFacility.pinCode === coachFacility.pinCode)
          );

          if (matchingAcademyFacility) {
            matchedFacilities.push({
              ...matchingAcademyFacility,
              facilityType: 'academy',
              location: coachFacility.location || {
                type: "Point",
                coordinates: [],
                is_location_exact: false,
              }
            });
          }
        }
      });

      setSelectedFacilities(matchedFacilities);
    }
  }, [coachData, academyFacilities]);

  // Add handler for coach share change
  const handleCoachShareChange = (e) => {
    const raw = e.target.value;

    // Allow clearing
    if (raw === "") {
      formik.setFieldValue("coachShare", "", true);
      formik.setFieldTouched("coachShare", true);
      formik.setFieldValue("academyShare", "");
      handleFormChange();
      return;
    }

    let num = Number(raw);
    if (Number.isNaN(num)) {
      return;
    }

    // Clamp to [0, 100]
    num = Math.min(Math.max(num, 0), 100);

    const coachShareValue = Number(num.toFixed(2));
    const academyShareValue = Number((100 - coachShareValue).toFixed(2));

    formik.setFieldValue("coachShare", coachShareValue, true);
    formik.setFieldTouched("coachShare", true);
    formik.setFieldValue("academyShare", academyShareValue);
    handleFormChange();
  };

  const getTenYearsLaterDate = () => {
    const currentDate = new Date();
    const tenYearsLater = new Date(
      currentDate.setFullYear(currentDate.getFullYear() + 10)
    );
    return tenYearsLater.toISOString();
  };

  return (
    <>
      <Seo
        title={`${coachData?.firstName || 'Coach'} - Coach at KhelSport`}
        description="Professional coach profile page on KhelSport. View coaching qualifications, experience, and contact details."
        canonical={`https://khelcoach.com/coaches/${coachData?._id || ''}`}
        url={`https://khelcoach.com/coaches/${coachData?._id || ''}`}
        jsonLd={schema.productOrPerson({
          type: "Person",
          name: `${coachData?.firstName || 'Coach'} ${coachData?.lastName || ''}`,
          description: "Professional coach profile on KhelSport",
          personProps: {
            jobTitle: "Coach",
            url: `https://khelcoach.com/coaches/${coachData?._id || ''}`,
          },
        })}
      />
      {/* Profile image */}
      <Card
        pointerEvents={
          !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
        }
      >
        <CardBody textAlign={"center"}>
          <Heading as="h4" size="md" mb={2} flexBasis={"30%"}>
            Profile Image
          </Heading>
          <Flex
            flexDirection={"column"}
            flexBasis={"70%"}
            min={formik.values.academyAvailability?.startTime || undefined}
            justifyContent={"center"}
            alignItems={"center"}
          >
            <Avatar
              size="2xl"
              name="profile-img"
              src={
                profileImagePreview ||
                "https://media.istockphoto.com/id/1300845620/vector/user-icon-flat-isolated-on-white-background-user-symbol-vector-illustration.jpg?s=612x612&w=0&k=20&c=yBeyba0hUkh14_jgv1OKqIH0CCSWU_4ckRkAoy2p73o="
              }
            />
            <Flex mt={2}>
              {userData?.accessScopes?.coach?.includes("write") && (
                <Tooltip label="Edit Profile">
                  <Box position="relative" ml={2}>
                    <Input
                      id="fileInput"
                      type="file"
                      accept=".jpg,.jpeg,.png,image/jpeg,image/jpg,image/png"
                      onChange={(e) => handleProfileImageChange(e)}
                      style={{ display: "none" }}
                      multiple={false}
                    />
                    <Text
                      as="label"
                      htmlFor="fileInput"
                      fontSize="22px"
                      cursor="pointer"
                    >
                      <FaEdit />
                    </Text>
                    {formik?.touched?.profileImg && formik?.errors?.profileImg && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formik?.errors?.profileImg}
                      </Text>
                    )}
                  </Box>
                </Tooltip>
              )}
              {defaultImageUrl !== profileImagePreview &&
                userData?.accessScopes?.coach?.includes("delete") && (
                  <Tooltip label="Delete Profile">
                    <Text
                      ml={1}
                      as={"span"}
                      fontSize={"22px"}
                      cursor={"pointer"}
                      onClick={() => deleteImageFiles(profileImagePreview)}
                    >
                      <MdDelete />
                    </Text>
                  </Tooltip>
                )}
            </Flex>
          </Flex>
        </CardBody>
      </Card>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          formik.handleSubmit(e);
        }}
      >
        {/* Personal Info */}
        <Card
          mt={4}
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
              Personal Information
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} my={4}>
              <FormControl
                isInvalid={formik.touched.firstName && formik.errors.firstName}
              >
                <FormLabel htmlFor="firstName">First Name<Text as="span" color="red.500">*</Text></FormLabel>
                <Input
                  type="text"
                  placeholder="Enter first name"
                  name="firstName"
                  id="firstName"
                  {...formik.getFieldProps("firstName")}
                  autoComplete="given-name"
                  onChange={(e) => {
                    formik.handleChange(e);
                    handleFormChange();
                  }}
                />
                <FormErrorMessage>{formik.errors.firstName}</FormErrorMessage>
              </FormControl>
              <FormControl
                isInvalid={formik.touched.lastName && formik.errors.lastName}
              >
                <FormLabel htmlFor="lastName">Last Name<Text as="span" color="red.500">*</Text></FormLabel>
                <Input
                  type="text"
                  placeholder="Enter last name"
                  name="lastName"
                  id="lastName"
                  autoComplete="family-name"
                  {...formik.getFieldProps("lastName")}
                  onChange={(e) => {
                    formik.handleChange(e);
                    handleFormChange();
                  }}
                />
                <FormErrorMessage>{formik.errors.lastName}</FormErrorMessage>
              </FormControl>
            </SimpleGrid>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} my={4}>
              <FormControl
                isInvalid={formik.touched.mobile && formik.errors.mobile}
              >
                <FormLabel htmlFor="mobile">Mobile<Text as="span" color="red.500">*</Text></FormLabel>
                <Input
                  type="number"
                  placeholder="Enter mobile number"
                  name="mobile"
                  id="mobile"
                  autoComplete="given-name"
                  {...formik.getFieldProps("mobile")}
                  onChange={(e) => {
                    formik.handleChange(e);
                    handleFormChange();
                  }}
                />
                <FormErrorMessage>{formik.errors.mobile}</FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={formik.touched.alternateMobile && formik.errors.alternateMobile}>
                <FormLabel htmlFor="alternateMobile">
                  Alternate Mobile
                </FormLabel>
                <Input
                  type="number"
                  placeholder="Enter alternate mobile number"
                  name="alternateMobile"
                  id="alternateMobile"
                  autoComplete="given-name"
                  {...formik.getFieldProps("alternateMobile")}
                  onChange={(e) => {
                    formik.handleChange(e);
                    handleFormChange();
                  }}
                />
                <FormErrorMessage>
                  {formik.errors.alternateMobile}
                </FormErrorMessage>
              </FormControl>
            </SimpleGrid>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} my={4}>
              <FormControl
                isInvalid={formik.touched.email && formik.errors.email}
              >
                <FormLabel htmlFor="email">Email Address<Text as="span" color="red.500">*</Text></FormLabel>
                <Input
                  type="email"
                  placeholder="Enter email address"
                  name="email"
                  autoComplete="email"
                  {...formik.getFieldProps("email")}
                  isDisabled={coachData ? true : false}
                />
                <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
              </FormControl>
              <FormControl
                isInvalid={formik.touched.password && formik.errors.password}
              >
                <FormLabel htmlFor="password">Password<Text as="span" color="red.500">*</Text></FormLabel>
                <InputGroup>
                  <InputLeftElement
                    pointerEvents="none"
                    color="gray.300"
                    children={<CFaLock color="gray.300" />}
                  />
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter password"
                    id="password"
                    name="password"
                    autoComplete="off"
                    {...formik.getFieldProps("password")}
                    isDisabled={coachData ? true : false}
                  />
                  {!coachData && (
                    <InputRightElement width="4.5rem">
                      <Button h="1.75rem" size="sm" onClick={handleShowClick}>
                        {showPassword ? "Hide" : "Show"}
                      </Button>
                    </InputRightElement>
                  )}
                </InputGroup>
                <FormErrorMessage>{formik.errors.password}</FormErrorMessage>
              </FormControl>
            </SimpleGrid>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} my={4}>
              <FormControl isInvalid={formik.touched.dob && formik.errors.dob}>
                <FormLabel htmlFor="dob">Date of Birth<Text as="span" color="red.500">*</Text></FormLabel>
                <ReactDatePicker
                  placeholderText="Select date of birth"
                  selected={formik.values.dob}
                  onChange={(date) => {
                    formik.setFieldValue("dob", date);
                    handleFormChange();
                  }}
                  dateFormat="dd MMMM yyyy"
                  peekNextMonth
                  showMonthDropdown
                  showYearDropdown
                  maxDate={new Date()}
                  dropdownMode="select"
                  popperClassName="react-datepicker-portal"
                />
                <FormErrorMessage>{formik.errors.dob}</FormErrorMessage>
              </FormControl>
              <FormControl
                isInvalid={formik.touched.gender && formik.errors.gender}
              >
                <FormLabel htmlFor="gender">Gender<Text as="span" color="red.500">*</Text></FormLabel>
                <Select
                  placeholder="Select Gender"
                  type="text"
                  name="gender"
                  id="gender"
                  autoComplete="family-name"
                  {...formik.getFieldProps("gender")}
                  onChange={(e) => {
                    formik.handleChange(e);
                    handleFormChange();
                  }}
                >
                  <option value="female">Female</option>
                  <option value="male">Male</option>
                  <option value="other">Other</option>
                </Select>
                <FormErrorMessage>{formik.errors.gender}</FormErrorMessage>
              </FormControl>
            </SimpleGrid>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} my={4}>
              <FormControl
                my={4}
                isInvalid={
                  formik.touched.coachShare && !!formik.errors.coachShare
                }
              >
                <FormLabel>Coach Share (%)<Text as="span" color="red.500">*</Text></FormLabel>
                <Input
                  type="number"
                  name="coachShare"
                  value={formik.values.coachShare != null ? formik.values.coachShare : ""}
                  onChange={handleCoachShareChange}
                  min={0}
                  max={100}
                  step="0.01"
                  placeholder="Coach Share"
                  onWheel={(e) => e.target.blur()}
                />
                <FormErrorMessage>{formik.errors.coachShare}</FormErrorMessage>
              </FormControl>

              <FormControl
                my={4}
                isInvalid={
                  formik.touched.academyShare && formik.errors.academyShare
                }
              >
                <FormLabel>Academy Share (%)</FormLabel>
                <Input
                  type="number"
                  name="academyShare"
                  value={formik.values.academyShare}
                  onChange={formik.handleChange}
                  min={0}
                  max={100}
                  step="0.01"
                  placeholder="Academy Share"
                  isReadOnly
                  bg="gray.100"
                  cursor="not-allowed"
                />
                <FormErrorMessage>
                  {formik.errors.academyShare}
                </FormErrorMessage>
              </FormControl>
            </SimpleGrid>

            {shareError && (
              <Text color="red.500" fontSize="sm" mb={2}>
                {shareError}
              </Text>
            )}
            <Box my={4} p={4} borderWidth={1} borderRadius="md">
              <FormLabel>Coach Availability</FormLabel>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} mb={2}>
                <FormControl isInvalid={formik.touched.academyAvailability?.startDate && !!formik.errors.academyAvailability?.startDate}>
                  <FormLabel>Start Date<Text as="span" color="red.500">*</Text></FormLabel>

                  <Input
                    type="date"
                    name="academyAvailability.startDate"
                    id="academyAvailability.startDate"
                    value={formik.values.academyAvailability?.startDate || ""}
                    onChange={(e) => {
                      const selectedDate = e.target.value;
                      const today = new Date().toISOString().split("T")[0];
                      const initialStartDate = initialValuesRef.current?.academyAvailability?.startDate;

                      formik.handleChange(e);
                      handleFormChange();

                      // For existing coaches, don't set errors during onChange
                      // This prevents errors from blocking form submission when other fields are updated
                      if (!coachData) {
                        // Only validate past date for new coaches
                        if (selectedDate && selectedDate < today) {
                          formik.setFieldError("academyAvailability.startDate", "Start date cannot be in the past");
                        } else {
                          formik.setFieldError("academyAvailability.startDate", undefined);
                        }
                      }
                    }}
                    min={(() => {
                      // Disable native min validation for existing coaches unless start date changed
                      const today = new Date().toISOString().split("T")[0];
                      const initialStartDate = initialValuesRef.current?.academyAvailability?.startDate;
                      const currentStartDate = formik.values.academyAvailability?.startDate;
                      const isChanged = initialStartDate !== currentStartDate;
                      return (!coachData || isChanged) ? today : undefined;
                    })()}
                  />
                  {formik.touched.academyAvailability?.startDate &&
                    formik.errors.academyAvailability?.startDate && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formik.errors.academyAvailability.startDate}
                      </Text>
                    )}
                </FormControl>
                <FormControl isInvalid={formik.touched.academyAvailability?.endDate && formik.errors.academyAvailability?.endDate}>
                  <FormLabel>End Date<Text as="span" color="red.500">*</Text></FormLabel>

                  <Input
                    type="date"
                    name="academyAvailability.endDate"
                    id="academyAvailability.endDate"
                    value={formik.values.academyAvailability?.endDate || ""}
                    onChange={(e) => {
                      const startDate = formik.values.academyAvailability?.startDate;
                      const endDate = e.target.value;
                      const today = new Date().toISOString().split("T")[0];

                      formik.handleChange(e);
                      handleFormChange();

                      // For existing coaches, don't set errors during onChange
                      // This prevents errors from blocking form submission when other fields are updated
                      if (!coachData) {
                        // Only validate for new coaches
                        if (endDate && endDate < today) {
                          formik.setFieldError("academyAvailability.endDate", "End date cannot be in the past");
                        } else if (startDate && endDate && endDate <= startDate) {
                          formik.setFieldError("academyAvailability.endDate", "End date must be after start date");
                        } else {
                          formik.setFieldError("academyAvailability.endDate", undefined);
                        }
                      }
                    }}
                    min={(() => {
                      const startDate = formik.values.academyAvailability?.startDate;
                      if (startDate) {
                        const dayAfterStart = new Date(startDate);
                        dayAfterStart.setDate(dayAfterStart.getDate() + 1);
                        return dayAfterStart.toISOString().split("T")[0];
                      }
                      return undefined;
                    })()}
                  />
                  {formik.touched.academyAvailability?.endDate &&
                    formik.errors.academyAvailability?.endDate && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formik.errors.academyAvailability.endDate}
                      </Text>
                    )}
                </FormControl>
              </SimpleGrid>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} mb={2}>
                <FormControl
                  isInvalid={
                    formik.touched.academyAvailability?.startTime &&
                    !!formik.errors.academyAvailability?.startTime
                  }
                >
                  <FormLabel>
                    Start Time
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  </FormLabel>
                  <TimeSelect
                    name="academyAvailability.startTime"
                    id="academyAvailability.startTime"
                    value={formik.values.academyAvailability?.startTime || ""}
                    placeholder="Select start time"
                    selectedDate={formik.values.academyAvailability?.startDate || ""}
                    onChange={(e) => {
                      formik.handleChange(e);
                      handleFormChange();
                      const startTime = e.target.value;
                      const endTime = formik.values.academyAvailability?.endTime;
                      if (startTime && endTime) {
                        const startMinutes = timeToMinutes(startTime);
                        const endMinutes = timeToMinutes(endTime);
                        if (endMinutes < startMinutes + 30) {
                          formik.setFieldError("academyAvailability.endTime", "End time must be at least 30 minutes after start time");
                        } else {
                          formik.setFieldError("academyAvailability.endTime", undefined);
                        }
                      }
                    }}
                    isInvalid={formik.touched.academyAvailability?.startTime && !!formik.errors.academyAvailability?.startTime}
                  />
                  {formik.touched.academyAvailability?.startTime &&
                    formik.errors.academyAvailability?.startTime && (
                      <Text
                        color="red.500"
                        fontSize="sm"
                        mt={1}
                        fontWeight="medium"

                        p={2}
                        borderRadius="md"


                      >
                        {formik.errors.academyAvailability.startTime}
                      </Text>
                    )}
                </FormControl>
                <FormControl
                  isInvalid={
                    formik.touched.academyAvailability?.endTime &&
                    formik.errors.academyAvailability?.endTime
                  }
                >
                  <FormLabel>
                    End Time
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  </FormLabel>
                  <TimeSelect
                    name="academyAvailability.endTime"
                    id="academyAvailability.endTime"
                    value={formik.values.academyAvailability?.endTime || ""}
                    placeholder="Select end time"
                    disabled={!formik.values.academyAvailability?.startTime}
                    selectedDate={formik.values.academyAvailability?.startDate || ""}
                    minTime={(() => {
                      const start = formik.values.academyAvailability?.startTime;
                      if (!start) return undefined;
                      const startMinutes = timeToMinutes(start);
                      const minEnd = startMinutes + 30;
                      const rounded = Math.ceil(minEnd / 15) * 15;
                      return minutesToTimeString(rounded);
                    })()}
                    onChange={(e) => {
                      const startTime = formik.values.academyAvailability?.startTime;
                      const endTime = e.target.value;
                      formik.handleChange(e);
                      handleFormChange();
                      if (startTime && endTime) {
                        const startMinutes = timeToMinutes(startTime);
                        const endMinutes = timeToMinutes(endTime);
                        if (endMinutes < startMinutes + 30) {
                          formik.setFieldError("academyAvailability.endTime", "End time must be at least 30 minutes after start time");
                        } else {
                          formik.setFieldError("academyAvailability.endTime", undefined);
                        }
                      }
                    }}
                    isInvalid={formik.touched.academyAvailability?.endTime && formik.errors.academyAvailability?.endTime}
                  />
                  {formik.touched.academyAvailability?.endTime &&
                    formik.errors.academyAvailability?.endTime && (
                      <Text
                        color="red.500"
                        fontSize="sm"
                        mt={1}
                        fontWeight="medium"
                        p={2}
                      >
                        {formik.errors.academyAvailability.endTime}
                      </Text>
                    )}
                </FormControl>
              </SimpleGrid>
              <Box className="sm:col-span-3" mb={4}>
                <FormLabel fontWeight="medium" fontSize="md" color="gray.900">
                  Select Days<Text as="span" color="red.500">*</Text>
                </FormLabel>
                <Flex mt={2} gap={2} flexWrap="wrap" justifyContent="left">
                  {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map(
                    (day) => (
                      <Button
                        key={day}
                        size="md"
                        px={6}
                        py={4}
                        fontSize="lg"
                        variant={
                          formik.values.academyAvailability?.days?.includes(day)
                            ? "solid"
                            : "outline"
                        }
                        colorScheme={
                          formik.values.academyAvailability?.days?.includes(day)
                            ? "blue"
                            : "gray"
                        }
                        fontWeight={
                          formik.values.academyAvailability?.days?.includes(day)
                            ? "bold"
                            : "normal"
                        }
                        onClick={async () => {
                          const selected =
                            formik.values.academyAvailability?.days || [];
                          const updated = selected.includes(day)
                            ? selected.filter((d) => d !== day)
                            : [...selected, day];

                          // Update the field value and wait for it to complete
                          await formik.setFieldValue(
                            "academyAvailability.days",
                            updated
                          );

                          // Mark the field as touched
                          await formik.setFieldTouched("academyAvailability.days", true, false);

                          // Immediately handle validation based on the updated array
                          // Use setTimeout to ensure the state has been updated before clearing/setting error
                          setTimeout(() => {
                            if (updated.length > 0) {
                              // Clear the error immediately when we have at least one day
                              formik.setFieldError("academyAvailability.days", undefined);
                            } else {
                              // Set error when no days are selected
                              formik.setFieldError("academyAvailability.days", "Select at least one day");
                            }
                          }, 0);

                          handleFormChange();
                        }}
                      >
                        {day}
                      </Button>
                    )
                  )}
                </Flex>
                {formik.touched.academyAvailability?.days &&
                  formik.errors.academyAvailability?.days && (
                    <Text color="red.500" fontSize="sm">
                      {formik.errors.academyAvailability.days}
                    </Text>
                  )}
              </Box>
            </Box>
          </CardBody>
        </Card>
        {/* Academy Facilities Selection */}
        <Card my={4}>
          {academyFacilities.length > 0 && (
            <CardBody>
              <Heading as="h4" size="md" mb={4}>
                Select Academy Facilities
              </Heading>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                {academyFacilities.map((facility) => (
                  <Box key={facility._id} p={4} borderWidth={1} borderRadius="md">
                    <Flex alignItems="flex-start" mb={2}>
                      <Checkbox
                        id={`facility-${facility._id}`}
                        isChecked={selectedFacilities.some(f => f._id === facility._id)}
                        onChange={(e) => handleFacilitySelection(facility, e.target.checked)}
                        mr={3}
                        mt={1}
                      />
                      <Box flex={1}>
                        <Text fontWeight="bold" fontSize="md">{facility.name}</Text>
                        <Text fontSize="sm" color="gray.600">
                          {facility.addressLine1}
                          {facility.addressLine2 && `, ${facility.addressLine2}`}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          {facility.city}, {facility.state} - {facility.pinCode}
                        </Text>
                        <Text fontSize="sm" color="gray.600">{facility.country}</Text>
                        {facility.amenities && (
                          <Text fontSize="xs" color="gray.500" mt={1} noOfLines={2}>
                            Amenities: {facility.amenities.replace(/<[^>]*>/g, '')}
                          </Text>
                        )}
                      </Box>
                    </Flex>
                  </Box>
                ))}
              </SimpleGrid>
            </CardBody>
          )}
          {/* Facility Validation Error */}
          {formik.errors.facilities && (
            <CardBody>
              <Text color="red.500" fontSize="sm">
                {formik.errors.facilities}
              </Text>
            </CardBody>
          )}
          {/* Manual Facilities */}
          {formik.values.linkedFacilities.length > 0 && (

            <CardBody mb={4} mx={5} border="1px solid" borderColor="gray.200" borderRadius="md"
              pointerEvents={
                !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
              }>
              <Flex
                justifyContent={"space-between"}
                alignItems={"center"}
                mb={4}
                px={5}
              >
                <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
                  Manual Facilities
                </Heading>
                <Button
                  colorScheme="green"
                  size={"sm"}
                  px={4}
                  onClick={addAddress}
                >
                  Add
                </Button>
              </Flex>
              {formik.values.linkedFacilities.length > 0 && <Divider mb={4} mx={5} />}
              {formik?.values?.linkedFacilities?.map((address, index) => {
                return (
                  <Box key={index}>
                    <CardBody>
                      <Flex
                        justifyContent={"space-between"}
                        alignItems={"center"}
                        mb={4}
                      >
                        <Heading as="h4" size="md" mb={0}>
                          {"Address " + (index + 1)}
                        </Heading>
                        {index + 1 > 1 ? (
                          <Button
                            colorScheme="red"
                            size={"sm"}
                            px={4}
                            onClick={() =>
                              formik.setValues((prevState) => ({
                                ...prevState,
                                linkedFacilities:
                                  prevState.linkedFacilities.filter(
                                    (_, inx) => inx !== index
                                  ),
                              }))
                            }
                          >
                            Remove
                          </Button>
                        ) : null}
                      </Flex>
                      <Divider />
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} my={4}>
                        <FormControl
                          isInvalid={
                            formik.touched.linkedFacilities?.[index]?.name &&
                            formik.errors.linkedFacilities?.[index]?.name
                          }
                        >
                          <FormLabel htmlFor={`linkedFacilities.${index}.name`}>
                            Facility Name
                            <Text as="span" color="red.500">
                              *
                            </Text>
                          </FormLabel>
                          <Input
                            type="text"
                            name={`linkedFacilities.${index}.name`}
                            id={`linkedFacilities.${index}.name`}
                            autoComplete="given-name"
                            placeholder="Enter facility name"
                            {...formik.getFieldProps(
                              `linkedFacilities.${index}.name`
                            )}
                            onBlur={(e) => {
                              formik.handleBlur(e);
                              updateFacilityCoordinates(index);
                            }}
                          />
                          {formik.touched.linkedFacilities?.[index]?.name &&
                            formik.errors.linkedFacilities?.[index]?.name && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {formik.errors.linkedFacilities?.[index]?.name}
                              </Text>
                            )}
                        </FormControl>
                        <FormControl
                          isInvalid={
                            formik.touched.linkedFacilities?.[index]?.pinCode &&
                            formik.errors.linkedFacilities?.[index]?.pinCode
                          }
                        >
                          <FormLabel
                            htmlFor={`linkedFacilities.${index}.pincode`}
                          >
                            Pincode
                            <Text as="span" color="red.500">
                              *
                            </Text>
                          </FormLabel>
                          <Input
                            type="number"
                            placeholder="Enter pincode"
                            name={`linkedFacilities.${index}.pinCode`}
                            id={`linkedFacilities.${index}.pinCode`}
                            // autoComplete="family-name"
                            value={formik.values.linkedFacilities[index].pinCode}
                            onChange={(e) => {
                              const newValue = e.target.value;
                              // Only allow numeric input and limit to reasonable length
                              if (/^\d*$/.test(newValue) && newValue.length <= 8) {
                                formik.setFieldValue(`linkedFacilities[${index}].pinCode`, newValue);

                                // Trigger validation when user types exactly 6 digits
                                if (newValue.length === 6) {
                                  getDetailsFromPincode(newValue, index);
                                } else if (newValue.length !== 6) {
                                  // Reset error state if pincode is not 6 digits
                                  setPincodeError(false);
                                }
                              }
                            }}
                          // {...formik.getFieldProps(
                          //   `linkedFacilities.${index}.pinCode`
                          // )}
                          />
                          {formik.touched.linkedFacilities?.[index]?.pinCode &&
                            formik.errors.linkedFacilities?.[index]?.pinCode && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {formik.errors.linkedFacilities?.[index]?.pinCode}
                              </Text>
                            )}
                        </FormControl>
                      </SimpleGrid>
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} my={4}>
                        <FormControl
                          isInvalid={
                            formik.touched.linkedFacilities?.[index]
                              ?.addressLine1 &&
                            formik.errors.linkedFacilities?.[index]?.addressLine1
                          }
                        >
                          <FormLabel
                            htmlFor={`linkedFacilities.${index}.addressLine1`}
                          >
                            Address Line 1<Text as="span" color="red.500">
                              *
                            </Text>
                          </FormLabel>
                          <Input
                            type="text"
                            placeholder="Enter address line 1"
                            name={`linkedFacilities.${index}.addressLin1`}
                            id={`linkedFacilities.${index}.addressLine1`}
                            autoComplete="given-name"
                            {...formik.getFieldProps(
                              `linkedFacilities.${index}.addressLine1`
                            )}
                            onBlur={(e) => {
                              formik.handleBlur(e);
                              updateFacilityCoordinates(index);
                            }}
                          />
                          {formik.touched.linkedFacilities?.[index]
                            ?.addressLine1 &&
                            formik.errors.linkedFacilities?.[index]
                              ?.addressLine1 && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {
                                  formik.errors.linkedFacilities?.[index]
                                    ?.addressLine1
                                }
                              </Text>
                            )}
                        </FormControl>
                        <FormControl>
                          <FormLabel
                            htmlFor={`linkedFacilities.${index}.addressLine2`}
                          >
                            Address Line 2
                          </FormLabel>
                          <Input
                            type="text"
                            placeholder="Enter address line 2"
                            name={`linkedFacilities.${index}.addressLine2`}
                            id={`linkedFacilities.${index}.addressLine2`}
                            autoComplete="family-name"
                            {...formik.getFieldProps(
                              `linkedFacilities.${index}.addressLine2`
                            )}
                          />
                        </FormControl>
                      </SimpleGrid>
                      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} my={4}>
                        <FormControl
                          isInvalid={
                            formik.touched.linkedFacilities?.[index]?.city &&
                            formik.errors.linkedFacilities?.[index]?.city
                          }
                        >
                          <FormLabel htmlFor={`linkedFacilities.${index}.city`}>
                            City<Text as="span" color="red.500">
                              *
                            </Text>
                          </FormLabel>
                          <Input
                            type="text"
                            placeholder="Enter city name"
                            name={`linkedFacilities.${index}.city`}
                            id={`linkedFacilities.${index}.city`}
                            autoComplete="family-name"
                            {...formik.getFieldProps(
                              `linkedFacilities.${index}.city`
                            )}
                          />
                          {formik.touched.linkedFacilities?.[index]?.city &&
                            formik.errors.linkedFacilities?.[index]?.city && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {formik.errors.linkedFacilities?.[index]?.city}
                              </Text>
                            )}
                        </FormControl>
                        <FormControl
                          isInvalid={
                            formik.touched.linkedFacilities?.[index]?.state &&
                            formik.errors.linkedFacilities?.[index]?.state
                          }
                        >
                          <FormLabel htmlFor={`linkedFacilities.${index}.state`}>
                            State<Text as="span" color="red.500">
                              *
                            </Text>
                          </FormLabel>
                          <Input
                            type="text"
                            placeholder="Enter state name"
                            name={`linkedFacilities.${index}.state`}
                            id={`linkedFacilities.${index}.state`}
                            autoComplete="family-name"
                            {...formik.getFieldProps(
                              `linkedFacilities.${index}.state`
                            )}
                          />
                          {formik.touched.linkedFacilities?.[index]?.state &&
                            formik.errors.linkedFacilities?.[index]?.state && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {formik.errors.linkedFacilities?.[index]?.state}
                              </Text>
                            )}
                        </FormControl>
                        <FormControl
                          isInvalid={
                            formik.touched.linkedFacilities?.[index]?.country &&
                            formik.errors.linkedFacilities?.[index]?.country
                          }
                        >
                          <FormLabel
                            htmlFor={`linkedFacilities.${index}.country`}
                          >
                            Country<Text as="span" color="red.500">
                          *
                            </Text>
                          </FormLabel>
                          <Input
                            type="text"
                            placeholder="Enter country name"
                            name={`linkedFacilities.${index}.country`}
                            id={`linkedFacilities.${index}.country`}
                            autoComplete="family-name"
                            {...formik.getFieldProps(
                              `linkedFacilities.${index}.country`
                            )}
                          />
                          {formik.touched.linkedFacilities?.[index]?.country &&
                            formik.errors.linkedFacilities?.[index]?.country && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {formik.errors.linkedFacilities?.[index]?.country}
                              </Text>
                            )}
                        </FormControl>
                      </SimpleGrid>
                      <FormControl flexBasis={"48%"}>
                        <FormLabel
                          htmlFor={`linkedFacilities.${index}.amenities`}
                        >
                          Ameneties
                        </FormLabel>
                        <ReactQuill
                          theme="snow"
                          value={formik.values.linkedFacilities[index].amenities}
                          onChange={(value) => {
                            formik.setFieldValue(
                              `linkedFacilities.${index}.amenities`,
                              value
                            );
                          }}
                        />
                      </FormControl>
                    </CardBody>
                    {index < formik.values.linkedFacilities.length - 1 && (
                      <Divider my={4} mx={5} />
                    )}
                  </Box>
                );
              })}
            </CardBody>
          )}

          {formik.values.linkedFacilities.length === 0 && (
            <CardBody>
              <Heading as="h4" size="md" mb={4}>
                Manual Facilities
              </Heading>
              <Button
                colorScheme="green"
                size="md"
                onClick={addAddress}
              >
                Add Manual Facility
              </Button>
            </CardBody>
          )}
        </Card>
        {userData?.accessScopes?.coach?.includes("write") && (
          <Flex justifyContent={"space-between"} alignItems={"center"}>
            <Button
              colorScheme="red"
              flexBasis={"49%"}
              isDisabled={isDiscardDisabled || !formChanged}
              onClick={() => {
                formik.resetForm();
                setIsDiscardDisabled(true);
                setFormChanged(false);
                toast({
                  title: "All Changes has been discarded",
                  status: "success",
                  duration: 4000,
                  position: "top",
                  isClosable: true,
                });
              }}
            >
              Discard
            </Button>
            <Button
              colorScheme="green"
              flexBasis={"49%"}
              type="submit"
              isLoading={isSubmitBtnLoading}
            >
              {coachData ? "Update" : "Submit"}
            </Button>
          </Flex>
        )}
      </form>
    </>
  );
};

export default BasicDetailsCoach;
	