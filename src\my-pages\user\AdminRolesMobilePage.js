import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  <PERSON><PERSON>,
  Card,
  <PERSON><PERSON>ody,
  <PERSON>ack,
  Toolt<PERSON>,
} from "@chakra-ui/react";
import { MdDele<PERSON>, MdEdit } from "react-icons/md";

export default function AdminRolesMobilePage({
  roles,
  isLoading,
  userData,
  handleEditRoleButtonClicked,
  openConfirmModal,
}) {
  return (
    <Box px={{base: 0, md: 1}} py={2}>
      {/* Card List */}
      <Stack spacing={4}>
        {roles && roles.length > 0 ? (
          roles.map((item, i) => (
              <Card key={item._id} border="1px solid #CBD5E0">
                <CardBody>
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>
                      S.No:
                    </Text>
                    <Text fontSize="sm" fontWeight="normal">
                      {i + 1}
                    </Text>
                  </Flex>
                  
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>
                      Name:
                    </Text>
                    <Text fontSize="sm" fontWeight="normal">
                      {item.name}
                    </Text>
                  </Flex>
                  
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>
                      Description:
                    </Text>
                    <Text fontSize="sm" fontWeight="normal" isTruncated maxW="150px">
                      {item.description || "n/a"}
                    </Text>
                  </Flex>

                  {/* Action Buttons */}
                  {item.name !== "Super Admin" ? (
                    <Flex justify="flex-end" mt={3} gap={2}>
                      {userData?.accessScopes?.user_group?.includes("write") && (
                        <Tooltip label="Edit Role">
                          <Button
                            size="sm"
                            colorScheme="blue"
                            variant="outline"
                            leftIcon={<MdEdit />}
                            onClick={() => handleEditRoleButtonClicked(item)}
                          >
                            Edit
                          </Button>
                        </Tooltip>
                      )}
                      {userData?.accessScopes?.user_group?.includes("delete") && (
                        <Tooltip label="Delete Role">
                          <Button
                            size="sm"
                            colorScheme="red"
                            variant="outline"
                            leftIcon={<MdDelete />}
                            onClick={() => openConfirmModal(item._id)}
                          >
                            Delete
                          </Button>
                        </Tooltip>
                      )}
                    </Flex>
                  ) : (
                    <Flex justify="flex-end" mt={3}>
                      <Badge variant="solid" colorScheme="purple">
                        System Role
                      </Badge>
                    </Flex>
                  )}
                </CardBody>
              </Card>
            ))
        ) : (
          <Text color="gray.500" textAlign="center" py={6}>
            No role details to show
          </Text>
        )}
      </Stack>
    </Box>
  );
}
