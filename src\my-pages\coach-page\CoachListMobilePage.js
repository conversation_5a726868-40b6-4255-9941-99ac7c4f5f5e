import {
    Box,
    Text,
    Button,
    Badge,
    Flex,
    Input,
    Select,
    Card,
    CardBody,
    Stack,
    InputGroup,
    InputRightAddon,
    Menu,
    MenuButton,
    MenuList,
    MenuItem,
    Tooltip,
  } from "@chakra-ui/react";
  import { FaCircleArrowRight } from "react-icons/fa6";
  import { useNavigate } from "react-router-dom";
  import { IoIosClose } from "react-icons/io";
  import { FaAngleDown } from "react-icons/fa";
  import { Link } from "react-router-dom";
  import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@chakra-ui/react";
  import { IoArrowBackCircleOutline } from "react-icons/io5";
  export default function CoachListMobileView({
  data,
  isLoading,
  searchCoachName,
  setSearchCoachName,
  selectedAuthStatus,
  setSelectedAuthStatus,
  selectedStatus,
  setSelectedStatus,
  handleAddCoach,
  userData,
  setCoachStatusChange,
  setCoachAuthStatusChange,
  onOpen3,
  onOpen4,
}) {
    const navigate = useNavigate();
  
    return (
      <Box px={{base: 0, md: 1}} py={0}>
        {/* Back Button and Heading */}
        <Flex alignItems="center" gap={0} mb={6}>
          <Button
            variant="ghost"
            size={{ base: "xs", md: "sm" }}
            leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
            onClick={() => navigate(-1)}
            _hover={{ bg: "gray.100" }}
            color="gray.700"
            fontWeight="bold"
            className="p-0"
          >
          </Button>
          <Breadcrumb fontWeight="medium" fontSize="18px">
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Coach</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
        </Flex>
        {/* Search */}
        <InputGroup mb={3}>
          <Input
            placeholder="Search"
            value={searchCoachName}
            onChange={(e) => setSearchCoachName(e.target.value)}
          />
          {searchCoachName && (
            <InputRightAddon
              bgColor={"gray.300"}
              border={"1px"}
              borderColor={"gray.300"}
              onClick={() => setSearchCoachName("")}
              cursor={"pointer"}
            >
              <IoIosClose fontSize={"24px"} />
            </InputRightAddon>
          )}
        </InputGroup>
  
        {/* Filters and Add Button */}
        <Flex gap={2} mb={4} direction="row" flexWrap="nowrap">
     <Select
      
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            flex="1"
            minW="0"
            
          >
            <option value="" disabled>Status</option>
            <option value="all">All</option>
            <option value="active">Active</option>
            <option value="inactive">In-Active</option>
          </Select>

          <Select
            value={selectedAuthStatus}
            onChange={(e) => setSelectedAuthStatus(e.target.value)}
            flex="1"
            minW="0"
          >
            <option value="" disabled>Auth Status</option>
            <option value="all">All</option>
            <option value="authorized">Authorized</option>
            <option value="unauthorized">Unauthorized</option>
          </Select>
          {userData?.accessScopes?.coach?.includes("write") && (
            <Button onClick={handleAddCoach} colorScheme="teal" variant="outline" flex="1" minW="100px" whiteSpace="nowrap">
              Add Coach
            </Button>
          )}
        </Flex>
  
                {/* Card List */}
        <Stack spacing={4}>
          {data && data.length > 0 ? (
            data.map((coach) => (
              <Card key={coach._id} border="1px solid #CBD5E0">
                <CardBody>
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Name:</Text>
                    <Text fontSize="sm" fontWeight="normal">{coach.firstName} {coach.lastName}</Text>
                  </Flex>
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Gender:</Text>
                    <Text fontSize="sm" fontWeight="normal">{coach.gender?.toUpperCase() || "n/a"}</Text>
                  </Flex>
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Experience:</Text>
                    <Text fontSize="sm" fontWeight="normal">{coach?.experience ? Math.trunc(coach.experience) + " Years" : "n/a"}</Text>
                  </Flex>
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Status:</Text>
                    {userData?.accessScopes?.coach?.includes("write") ? (
                      <Menu>
                        <Tooltip
                          label={
                            !coach?.kycDocuments?.documentImg?.length &&
                            "KYC details not available"
                          }
                        >
                          <MenuButton
                            variant={"outline"}
                            as={Button}
                            size={"xs"}
                            isDisabled={
                              !coach?.kycDocuments?.documentImg?.length
                            }
                            py={2}
                            colorScheme={
                              coach?.status === "active" ? "green" : "red"
                            }
                            rightIcon={<FaAngleDown />}
                          >
                            {coach?.status?.toUpperCase()}
                          </MenuButton>
                        </Tooltip>
                        <MenuList>
                          <MenuItem
                            isDisabled={coach?.status === "active"}
                            onClick={() => {
                              setCoachStatusChange({
                                type: "active",
                                id: coach?._id,
                              });
                              onOpen3();
                            }}
                          >
                            Active
                          </MenuItem>
                          <MenuItem
                            isDisabled={coach?.status !== "active"}
                            onClick={() => {
                              setCoachStatusChange({
                                type: "inactive",
                                id: coach?._id,
                              });
                              onOpen3();
                            }}
                          >
                            Inactive
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    ) : (
                      <Badge
                        colorScheme={coach.status === "active" ? "green" : "red"}
                      >
                        {coach.status?.toUpperCase()}
                      </Badge>
                    )}
                  </Flex>
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Auth Status:</Text>
                    {coach?.authStatus === "authorized" ? (
                      <Badge colorScheme="green">
                        Authorized
                      </Badge>
                    ) : (
                      userData?.accessScopes?.coach?.includes("write") ? (
                        <Menu>
                          <MenuButton
                            variant={"outline"}
                            as={Button}
                            size={"xs"}
                            py={2}
                            colorScheme={"red"}
                            rightIcon={<FaAngleDown />}
                          >
                            {coach?.authStatus?.toUpperCase()}
                          </MenuButton>
                          <MenuList>
                            <MenuItem
                              isDisabled={coach?.authStatus === "authorized"}
                              onClick={() => {
                                setCoachAuthStatusChange({
                                  type: "authorized",
                                  id: coach?._id,
                                });
                                onOpen4();
                              }}
                            >
                              Authorized
                            </MenuItem>
                          </MenuList>
                        </Menu>
                      ) : (
                        <Badge colorScheme="red">
                          {coach.authStatus?.toUpperCase()}
                        </Badge>
                      )
                    )}
                  </Flex>
                  {userData?.accessScopes?.coach?.includes("read") && (
                    <Flex justify="flex-end" mt={3}>
                      <Button
                        size="sm"
                        rightIcon={<FaCircleArrowRight />}
                        onClick={() =>
                          navigate(`/coach-page/details/${coach._id}`)
                        }
                      >
                        View Details
                      </Button>
                    </Flex>
                  )}
                </CardBody>
              </Card>
            ))
          ) : (
            <Text color="gray.500" textAlign="center" py={6}>
              No coach details to show
            </Text>
          )}
        </Stack>
      </Box>
    );
  }
  