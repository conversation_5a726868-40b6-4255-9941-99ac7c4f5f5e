import {
  Box,
  Text,
  Button,
  Flex,
  Input,
  Select,
  Stack,
  Spinner,
  InputGroup,
  InputRightAddon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Card,
  CardBody,
  Badge,
} from "@chakra-ui/react";
import { MdDownload } from "react-icons/md";
import ReactPaginate from "react-paginate";
import { IoMdSearch } from "react-icons/io";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import { useNavigate } from "react-router-dom";

export default function ReportMobileView({
  data,
  isLoading,
  startDate,
  endDate,
  minEndDate,
  maxEndDate,
  setStartDate,
  setEndDate,
  searchCourseName,
  setSearchCourseName,
  selectedStatus,
  setSelectedStatus,
  onSearch,
  onDownload,
  onCoachSearch,
  totalPages,
  currentPage,
  handlePageChange,
  onInvoiceDownload,
  onStatusChange,
}) {
  const navigate = useNavigate();
  return (
    <Box px={{base: 0, md: 1}} py={2}>
      <Flex alignItems="center" gap={0} mb={{base: 6, md: 0}}>
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
          className="p-0"
        >
        </Button>
        <Breadcrumb fontWeight="medium" fontSize="18px">
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Reports</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
      </Flex>
      {/* Date Range Row */}
      <Flex direction="row" gap={2} mb={2} alignItems="flex-end" justifyContent="center">
        <Box flex="1" minW={0} textAlign="center">
          <Text fontSize="sm" mb={0}>Start Date</Text>
          <Input
            type="date"
            value={startDate}
            max={maxEndDate}
            onChange={e => setStartDate(e.target.value)}
            size="sm"
          />
        </Box>
        <Box alignSelf="center" px={1}>
          <Text fontSize="xl" color="gray.500">-</Text>
        </Box>
        <Box flex="1" minW={0} textAlign="center">
          <Text fontSize="sm" mb={0}>End Date</Text>
          <Input
            type="date"
            value={endDate}
            min={minEndDate}
            max={maxEndDate}
            onChange={e => setEndDate(e.target.value)}
            size="sm"
          />
        </Box>
      </Flex>
      {/* Second line: Search and other buttons */}
      <Flex direction="row" gap={2} mb={4} alignItems="center" justifyContent="center">
        <Button
          variant={"outline"}
          colorScheme="teal"
          size={"sm"}
          px={4}
          onClick={onCoachSearch}
          flex="1"
        >
          Search
        </Button>
        <Select
          value={selectedStatus}
          onChange={e => setSelectedStatus(e.target.value)}
          flex="1"
          size="sm"
        >
          <option value="" disabled>Status</option>
          <option value="all">All</option>
          <option value="paid">Paid</option>
          <option value="unpaid">Unpaid</option>
        </Select>
        <Button
          onClick={onDownload}
          size="sm"
          flex="1"
        >
          Download
        </Button>
      </Flex>
      {/* Report Cards List */}
      <Stack spacing={4}>
        {isLoading ? (
          <Flex justify="center" align="center" py={10}>
            <Spinner />
          </Flex>
        ) : data && data.length > 0 ? (
          data.map((report, idx) => (
            <Card key={idx} border="1px solid #CBD5E0">
              <CardBody>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Booking ID:</Text>
                  <Text fontSize="sm" fontWeight="normal">{report.bookingId}</Text>
                </Flex>
                 <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Invoice:</Text>
                  <Text fontSize="sm" fontWeight="normal">{report.invoice}/{report.classIndex + 1}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Date:</Text>
                  <Text fontSize="sm" fontWeight="normal">{report.date?.split("T")[0]}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Training Schedule:</Text>
                  <Text fontSize="sm" fontWeight="normal">{report.courseName?.split(" ").slice(0, 4).join(" ")}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Coach:</Text>
                  <Text fontSize="sm" fontWeight="normal">{report.coachName}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Total Amount:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{(() => {
                    const classFees = report?.classFees || 0;
                    const gst = report.hasGst ? classFees * (18 / 118) : 0;
                    const baseAmount = classFees / 1.18;
                    const platformFee = baseAmount * 0.12;
                    const platformFeeGst = platformFee * 0.18;
                    const refund = report?.refundAmount || 0;
                    const total = gst + baseAmount + platformFee + platformFeeGst;
                    return total.toFixed(2);
                  })()}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Course Fees:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{((report?.classFees ?? 0) / 1.18).toFixed(2)}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Course GST:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{report.hasGst ? (report?.classFees * (18 / (100 + 18))).toFixed(2) : "0.00"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>UMN Fee:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{(report?.classFees ? (report.classFees / 1.18) * 0.12 : 0).toFixed(2)}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>UMN GST:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{(report?.classFees ? (report.classFees / 1.18) * 0.12 * 0.18 : 0).toFixed(2)}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Refund Amount:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{report?.refundAmount?.toFixed(2) || "0.00"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>TDS:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{report?.tds?.toFixed(2) || "0.00"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Amount Due to Coach:</Text>
                  <Text fontSize="sm" fontWeight="normal">₹{report?.amountReceived?.toFixed(2) || "0.00"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Coach Attendance:</Text>
                  <Badge colorScheme={report?.coachAttendance === "present" ? "green" : "red"} fontSize="xs">
                    {report?.coachAttendance || "N/A"}
                  </Badge>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Player Attendance:</Text>
                  <Badge colorScheme={report?.playerAttendance === "present" ? "green" : "red"} fontSize="xs">
                    {report?.playerAttendance || "N/A"}
                  </Badge>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Payment Status:</Text>
                  <Select
                    size="xs"
                    width="100px"
                    value={report?.paymentStatus}
                    onChange={(e) => onStatusChange(report, e.target.value)}
                    bgColor={report?.paymentStatus === "paid" ? "green.100" : "red.100"}
                    isDisabled={report?.paymentStatus === "paid"}
                  >
                    <option value="paid" style={{ color: "green" }}>Paid</option>
                    <option value="unpaid" style={{ color: "red" }}>Unpaid</option>
                  </Select>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Invoice:</Text>
                  <Button
                    leftIcon={<MdDownload />}
                    colorScheme="blue"
                    variant="link"
                    size="sm"
                    onClick={() => onInvoiceDownload(report)}
                  >
                    Download
                  </Button>
                </Flex>
              </CardBody>
            </Card>
          ))
        ) : (
          <Text color="gray.500" textAlign="center" py={6}>
            No report details to show
          </Text>
        )}
      </Stack>
      {/* Pagination */}
      {totalPages > 1 && (
        <Flex justify="center" align="center" mt={4}>
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
            forcePage={currentPage - 1}
          />
        </Flex>
      )}
    </Box>
  );
}
