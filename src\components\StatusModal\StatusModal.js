import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Text,
  VStack,
  Icon,
  Box,
} from "@chakra-ui/react";
import { MdWarning, MdBlock } from "react-icons/md";
import { deleteCookie } from "../../utilities/auth";
import { useNavigate } from "react-router-dom";

const StatusModal = ({ isOpen, type, onClose }) => {
  const navigate = useNavigate();

  const handleLogout = () => {
    sessionStorage.removeItem("admintoken");
    sessionStorage.removeItem("academyId");
    
    deleteCookie("userName");
    deleteCookie("email");
    deleteCookie("academyName");

    navigate("/login");
  };

  const getModalContent = () => {
    switch (type) {
      case "inactive":
        return {
          title: "Account Inactive",
          icon: MdBlock,
          iconColor: "red.500",
          message: "Your account is currently inactive. Please contact support to reactivate your account.",
          showLogout: true
        };
      case "kyc_pending":
        return {
          title: "KYC Details Pending",
          icon: MdWarning,
          iconColor: "orange.500",
          message: "Your KYC details are incomplete. Please complete your profile to access the dashboard.",
          showLogout: true
        };
      default:
        return {
          title: "Access Restricted",
          icon: MdWarning,
          iconColor: "red.500",
          message: "You cannot access the dashboard at this time.",
          showLogout: true
        };
    }
  };

  const content = getModalContent();

  return (
    <Modal isOpen={isOpen} onClose={() => {}} closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <VStack spacing={3}>
            <Box
              p={3}
              borderRadius="full"
              bg={`${content.iconColor.split('.')[0]}.100`}
            >
              <Icon as={content.icon} boxSize={8} color={content.iconColor} />
            </Box>
            <Text fontSize="lg" fontWeight="bold" textAlign="center">
              {content.title}
            </Text>
          </VStack>
        </ModalHeader>
        <ModalBody>
          <Text textAlign="center" color="gray.600">
            {content.message}
          </Text>
        </ModalBody>
        <ModalFooter justifyContent="center">
          {content.showLogout && (
            <Button colorScheme="red" onClick={handleLogout}>
              Logout
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default StatusModal;