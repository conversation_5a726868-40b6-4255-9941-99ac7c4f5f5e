import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Flex,
  Heading,
  Spinner,
  Text,
  useToast,
} from "@chakra-ui/react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import axios from "axios";
import { useSelector } from "react-redux";
import { jwtDecode } from "jwt-decode";
import { useNavigate } from "react-router-dom";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const DescriptionPageCms = () => {
  const navigate = useNavigate();
  const [descriptionData, setDescriptionData] = useState({
    result: null,
    isLoading: false,
    error: false,
  });
  const [isUpdated, setIsUpdated] = useState(false);
  const [discardBtnLoading, setDiscardBtnLoading] = useState(false);
  const [saveBtnLoading, setSaveBtnLoading] = useState(false);

  const Toast = useToast();
  const token = sessionStorage.getItem("admintoken")?.split(" ")[1];
  const userData = useSelector((state) => state.user);

  // Custom toast function to prevent multiple toasts
  const showToast = (title, status = "info", duration = 4000) => {
    // Close any existing toasts first
    Toast.closeAll();
    
    // Show new toast after a small delay
    setTimeout(() => {
      Toast({
        title,
        status,
        duration,
        position: "top",
        isClosable: true,
      });
    }, 100);
  };

  const getDescriptionData = () => {
    const decodedToken = jwtDecode(token);
    const academyId = decodedToken.academyId._id;

    setDescriptionData({ result: null, isLoading: true, error: false });
    axios
      .get(`${process.env.REACT_APP_BASE_URL}/api/academy-cms/${academyId}/description`)
      .then((response) => {
        const apiData = response.data.data;
        const originalHtml = apiData?.description || "";
        // Convert groups of spaces to &nbsp; for the editor to prevent collapsing
        const htmlForEditor = originalHtml.replace(/ {2,}/g, (m) => "&nbsp;".repeat(m.length));
        setDescriptionData({
          result: { ...apiData, description: htmlForEditor },
          isLoading: false,
          error: false,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong, please try again later";
        setDescriptionData({ result: null, isLoading: false, error: errorMessage });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
        showToast(errorMessage, "error");
      });
  };

  const updateDescriptionData = () => {
    // Preserve the exact HTML including spaces
    const descriptionHtml = descriptionData.result.description || "";
    // Before saving, convert &nbsp; back to normal spaces so DB keeps plain spaces
    const htmlForSaving = descriptionHtml.replace(/&nbsp;/g, " ");
    // Validate based on visible text content (strip HTML), but don't modify what we save
    const visibleText = htmlForSaving.replace(/<[^>]*>/g, "").trim();
    
    if (!visibleText || visibleText.length < 10) {
      setSaveBtnLoading(false);
      // Show validation error using custom toast function
      showToast("Please add at least 10 characters in description", "warning");
      return;
    }

    axios
      .post(
        `${process.env.REACT_APP_BASE_URL}/api/academy-cms/description`,
        { description: htmlForSaving },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then(() => {
        setSaveBtnLoading(false);
        setIsUpdated(false);
        showToast("Description updated successfully", "success");
      })
      .catch((error) => {
        setSaveBtnLoading(false);
        let errorMsg = "Something went wrong, please try again later";
        if (error.response && error.response.data) {
          if (error.response.data.errors && Array.isArray(error.response.data.errors) && error.response.data.errors.length > 0) {
            errorMsg = error.response.data.errors.map(e => e.message).join("\n");
          } else if (error.response.data.message) {
            errorMsg = error.response.data.message;
          } else if (error.response.data.error) {
            errorMsg = error.response.data.error;
          }
        }
        // Show error toast using custom toast function
        showToast(errorMsg, "error");
      });
  };

  useEffect(() => {
    getDescriptionData();
  }, []);

  return (
    <Box bgColor="#f2f2f2" minH="100vh">
      <Layout title="CMS | Description">
        {/* Responsive Back Button, Breadcrumb, and Action Buttons */}
        <Flex direction={{ base: "column", md: "row" }} alignItems={{ base: "flex-start", md: "center" }} justifyContent={{ base: "flex-start", md: "space-between" }} gap={{ base: 2, md: 0 }} pb={4}>
          <Flex alignItems="center" gap={0} mb={{ base: 3, md: 0 }}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize={{ base: "xs", sm: "sm" }} pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Description</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.cms?.includes("write") && (
            <Flex gap={2} direction={{ base: "row", sm: "row" }} w={{ base: "full", md: "auto" }} justifyContent={{ base: "flex-end", md: "flex-start" }}>
              <Button
                variant="outline"
                colorScheme="red"
                size={{ base: "xs", md: "sm" }}
                py={{ base: 4, md: 2 }}
                w={{ base: "35%", sm: "auto" }}
                isDisabled={!isUpdated}
                isLoading={discardBtnLoading}
                onClick={() => {
                  setDiscardBtnLoading(true);
                  getDescriptionData();
                }}
              >
                Discard
              </Button>
              <Button
                variant="solid"
                colorScheme="green"
                size={{ base: "xs", md: "sm" }}
                py={{ base: 4, md: 2 }}
                w={{ base: "35%", sm: "auto" }}
                isDisabled={!isUpdated}
                isLoading={saveBtnLoading}
                onClick={() => {
                  if (saveBtnLoading) return; // Prevent rapid clicking
                  setSaveBtnLoading(true);
                  updateDescriptionData();
                }}
              >
                Save Changes
              </Button>
            </Flex>
          )}
        </Flex>

        {/* Content Area */}
        {descriptionData.isLoading ? (
          <Flex justifyContent="center" alignItems="center" mt={12}>
            <Spinner size="lg" />
          </Flex>
        ) : descriptionData.error ? (
          <Flex justifyContent="center" alignItems="center" mt={12}>
            <Text color="red.500" textAlign="center" fontSize={{ base: "sm", md: "md" }}>
              {descriptionData?.error}
            </Text>
          </Flex>
        ) : (
          (descriptionData.result && descriptionData.result.description) ? (
            <Card mt={4}>
              <CardBody p={{ base: 4, md: 6 }}>
                <Heading as="h4" size={{ base: "sm", md: "md" }} mb={3}>
                  Academy Description
                </Heading>
                <Box sx={{ '.ql-editor': { whiteSpace: 'break-spaces' } }}>
                  <ReactQuill
                    theme="snow"
                    value={descriptionData.result.description || ""}
                    onChange={(value) => {
                      setDescriptionData((prev) => ({
                        ...prev,
                        result: { ...prev.result, description: value },
                      }));
                      // Enable button when content changes (including paste)
                      setIsUpdated(true);
                    }}
                    onPaste={() => {
                      // Ensure button is enabled when content is pasted
                      setIsUpdated(true);
                    }}
                    readOnly={!userData?.accessScopes?.cms?.includes("write")}
                    modules={!userData?.accessScopes?.cms?.includes("write") ? { toolbar: false } : {}}
                    style={{
                      minHeight: "200px",
                      fontSize: "14px",
                      borderRadius: "8px",
                    }}
                  />
                </Box>
              </CardBody>
            </Card>
          ) : (
            <Card mt={4}>
              <CardBody p={{ base: 4, md: 6 }}>
                <Heading as="h4" size={{ base: "sm", md: "md" }} mb={3}>
                  Add Academy Description
                </Heading>
                <Box sx={{ '.ql-editor': { whiteSpace: 'break-spaces' } }}>
                  <ReactQuill
                    theme="snow"
                    value={descriptionData.result?.description || ""}
                    onChange={(value) => {
                      setDescriptionData((prev) => ({
                        ...prev,
                        result: { ...(prev.result || {}), description: value },
                      }));
                      // Enable button when content changes (including paste)
                      setIsUpdated(true);
                    }}
                    onPaste={() => {
                      // Ensure button is enabled when content is pasted
                      setIsUpdated(true);
                    }}
                    readOnly={!userData?.accessScopes?.cms?.includes("write")}
                    modules={!userData?.accessScopes?.cms?.includes("write") ? { toolbar: false } : {}}
                    style={{
                      minHeight: "200px",
                      fontSize: "14px",
                      borderRadius: "8px",
                    }}
                  />
                  <Button
                    mt={4}
                    colorScheme="telegram"
                    size="md"
                    isLoading={saveBtnLoading}
                    isDisabled={!(descriptionData.result?.description?.trim() && descriptionData.result.description.trim().length >= 10)}
                    onClick={() => {
                      if (saveBtnLoading) return; // Prevent rapid clicking
                      setSaveBtnLoading(true);
                      updateDescriptionData();
                    }}
                  >
                    Add Description
                  </Button>
                </Box>
              </CardBody>
            </Card>
          )
        )}
      </Layout>
    </Box>
  );
};

export default DescriptionPageCms;
