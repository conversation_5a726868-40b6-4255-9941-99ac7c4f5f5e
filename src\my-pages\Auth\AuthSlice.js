import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isAuthenticated: false,
  token: '',
  user: null,
  error: null,
  academyProfileImage: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginSuccess(state, action) {
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.user = action.payload.user;
      state.error = null;
    },
    loginFailure(state, action) {
      state.isAuthenticated = false;
      state.token = '';
      state.user = null;
      state.error = action.payload.error;
    },
    setAcademyProfileImage(state, action) {
      state.academyProfileImage = action.payload;
    },
  }
});

export const { loginSuccess, loginFailure, setAcademyProfileImage } = authSlice.actions;

export default authSlice.reducer;
