import React from 'react'

const CoachAddress = () => {
  return (
    <Card mt={4}>
    <CardBody>
      <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
        Personal Information
      </Heading>
        <Flex justifyContent={'space-between'} alignItems={'center'} my={4}>
          <FormControl flexBasis={'48%'}>
            <FormLabel>First Name</FormLabel>
            <Input type="text" />
          </FormControl>
          <FormControl flexBasis={'48%'}>
            <FormLabel>Last Name</FormLabel>
            <Input type="text" />
          </FormControl>
        </Flex>
        <Flex justifyContent={'space-between'} alignItems={'center'} my={4}>
          <FormControl flexBasis={'48%'}>
            <FormLabel>Mobile</FormLabel>
            <Input type="number" />
          </FormControl>
          <FormControl flexBasis={'48%'}>
            <FormLabel>Alternate Mobile</FormLabel>
            <Input type="number" />
          </FormControl>
        </Flex>
        <Flex justifyContent={'space-between'} alignItems={'center'} my={4}>
          <FormControl flexBasis={'48%'}>
            <FormLabel>Email Address</FormLabel>
            <Input type="email" />
          </FormControl>
          <FormControl flexBasis={'48%'}>
            <FormLabel>Password</FormLabel>
            <Input type="password" />
          </FormControl>
        </Flex>
        <Flex justifyContent={'space-between'} alignItems={'center'} my={4}>
          <FormControl flexBasis={'48%'}>
            <FormLabel>Date of Birth</FormLabel>
            <Input type="date" />
          </FormControl>
          <FormControl flexBasis={'48%'}>
            <FormLabel>Gender</FormLabel>
            <Select placeholder='Select Gender'>
              <option value='Male'>Male</option>
              <option value='femaie'>Female</option>
              <option value='other'>Other</option>
            </Select>
          </FormControl>
        </Flex>
    </CardBody>
  </Card>
  )
}

export default CoachAddress