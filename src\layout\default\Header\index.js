import React, { useState, useEffect } from "react";
import { Dropdown } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import {
  Icon,
  LinkList,
  CustomDropdownMenu,
} from "../../../components";
import { useLayout, useLayoutUpdate } from "./../LayoutProvider";
import { deleteCookie, getCookie } from "../../../utilities/auth";
import axios from "axios";
import {
  Text,
  IconButton,
  Box,
  Button,
  useBreakpointValue,
  useToast,
} from "@chakra-ui/react";


function QuickNav({ children }) {
  return (
    <ul
      style={{
        listStyle: "none",
        padding: 0,
        margin: 0,
        display: "flex",
        alignItems: "center",
      }}
    >
      {children}
    </ul>
  );
}

function QuickNavItem({ children }) {
  return (
    <li style={{ listStyle: "none" }}>
      {children}
    </li>
  );
}

const CustomToggle = React.forwardRef(({ children, onClick }, ref) => (
  <div
    ref={ref}
    onClick={(e) => {
      e.preventDefault();
      onClick(e);
    }}
    style={{
      cursor: "pointer",
      outline: "none",
      border: "none",
      background: "transparent",
      display: "inline-flex",
      alignItems: "center",
    }}
  >
    {children}
  </div>
));
CustomToggle.displayName = "CustomToggle";


function Header() {

  const layout = useLayout();
  const layoutUpdate = useLayoutUpdate();
  const navigate = useNavigate();
  const toast = useToast();
  const showAcademyName = useBreakpointValue({ base: false, md: true });

  // Chakra breakpoint helpers
  const isDesktop = useBreakpointValue({ base: false, sm: true });

  const academyName = getCookie("academyName") || "";
  const userEmail = getCookie("email") || "";

  // State for profile image
  const [profileImageUrl, setProfileImageUrl] = useState(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);

  // Get academy ID from token
  const getAcademyIdFromToken = () => {
    const token = sessionStorage.getItem("admintoken");
    if (!token) return null;

    try {
      const decodedToken = JSON.parse(atob(token.split(" ")[1].split(".")[1]));
      return decodedToken.academyId?._id || decodedToken.academyId;
    } catch (error) {
      console.error("Error decoding token:", error);
      return null;
    }
  };

  // Fetch profile image from API
  useEffect(() => {
    const fetchProfileImage = async () => {
      const academyId = getAcademyIdFromToken();
      const token = sessionStorage.getItem("admintoken");

      if (!academyId || !token) return;

      setIsLoadingProfile(true);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_BASE_URL}/api/academy/${academyId}`,
          {
            headers: {
              Authorization: `Bearer ${token.split(" ")[1]}`,
            },
          }
        );

        if (response.data?.data?.profileImage) {
          setProfileImageUrl(response.data.data.profileImage);
        }
      } catch (error) {
        console.error("Error fetching profile image:", error);
        // Don't show toast for profile image fetch errors to avoid spam
      } finally {
        setIsLoadingProfile(false);
      }
    };

    fetchProfileImage();
  }, []); // Run once on component mount

  // Function to refresh profile image (can be called after profile updates)
  const refreshProfileImage = async () => {
    const academyId = getAcademyIdFromToken();
    const token = sessionStorage.getItem("admintoken");

    if (!academyId || !token) return;

    try {
      const response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/academy/${academyId}`,
        {
          headers: {
            Authorization: `Bearer ${token.split(" ")[1]}`,
          },
        }
      );

      if (response.data?.data?.profileImage) {
        setProfileImageUrl(response.data.data.profileImage);
      }
    } catch (error) {
      console.error("Error refreshing profile image:", error);
    }
  };

  const handleLogout = async () => {
    const token = sessionStorage.getItem("admintoken");
    try {
      const myHeaders = new Headers();
      if (token) {
        myHeaders.append("Authorization", token); // or Bearer token
      }

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        credentials: "include",
      };
      await fetch(`${process.env.REACT_APP_BASE_URL}/api/academy/logout`, requestOptions);
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      sessionStorage.removeItem("admintoken");
      localStorage.clear();
      deleteCookie("userName");
      navigate("/login");
    }
  };

  const handleForgetMe = async () => {
    const token = sessionStorage.getItem("admintoken");
    try {
      const myHeaders = new Headers();
      if (token) {
        myHeaders.append("Authorization", token);
      }

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        credentials: "include",
      };
      let academyId = null;
      if (token) {
        try {
          const decodedToken = JSON.parse(atob(token.split(" ")[1].split(".")[1]));
          academyId = decodedToken.academyId?._id || decodedToken.academyId;
        } catch (decodeError) {
          console.error("Error decoding token:", decodeError);
          toast({
            title: "Error",
            description: "Failed to decode authentication token.",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
          return;
        }
      }

      if (academyId) {
        const response = await fetch(`${process.env.REACT_APP_BASE_URL}/api/academy/delete/${academyId}`, requestOptions);

        if (!response.ok) {
          const errorData = await response.json();
          if (errorData.errors && Array.isArray(errorData.errors) && errorData.errors.length > 0) {
            const errorMessage = errorData.errors[0].message || "An error occurred";
            toast({
              title: "Cannot Delete Account",
              description: errorMessage,
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          } else {
            toast({
              title: "Error",
              description: errorData.message || "Failed to delete account. Please try again.",
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          }
          return;
        }
        toast({
          title: "Account Deleted",
          description: "Your account has been successfully deleted.",
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        sessionStorage.clear();
        localStorage.clear();
        deleteCookie("userName");
        deleteCookie("academyName");
        deleteCookie("email");
        navigate("/login");

      } else {
        console.warn(" Academy ID not found, skipping API call");
        toast({
          title: "Error",
          description: "Academy ID not found. Please try logging in again.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error("Forget me error:", error);
      toast({
        title: "Network Error",
        description: "Failed to connect to server. Please check your connection and try again.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };


  const MobileTrigger = (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: 36,
        height: 36,
        borderRadius: "50%",
        background: "linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%)",
        border: "2px solid #0e1014ff",
      }}
    >
      {profileImageUrl ? (
        <img
          src={profileImageUrl}
          alt="Academy Profile"
          style={{ width: 28, height: 28, borderRadius: "50%", objectFit: "cover" }}
        />
      ) : (
        <Icon
          style={{ width: 20, height: 20, color: "#3b82f6" }}
          name="user-alt"
        />
      )}
    </div>
  );

  const DesktopTrigger = (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        minWidth: 0,
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 44,
          height: 44,
          borderRadius: "50%",
          background: "linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%)",
          boxShadow: "0 2px 8px rgba(59,130,246,0.10)",
          border: "2px solid #0e1014ff",
          flexShrink: 0,
        }}
      >
        {profileImageUrl ? (
          <img
            src={profileImageUrl}
            alt="Academy Profile"
            style={{ width: 36, height: 36, borderRadius: "50%", objectFit: "cover" }}
          />
        ) : (
          <Icon
            style={{ width: 28, height: 28, color: "#3b82f6" }}
            name="user-alt"
          />
        )}
      </div>
    </div>
  );

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginTop: "20px",
        height: "80px",
        padding: "0 16px",
        backgroundColor: "#fff",
        borderBottom: "1px solid #e5e7eb",
        position: "relative",
        width: "100%",
        boxSizing: "border-box",
      }}
    >
      {/* Left: Heading and mobile sidebar toggle */}
      <div style={{ display: "flex", alignItems: "center" }}>
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <IconButton
            icon={
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                width="24px"
                height="24px"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                />
              </svg>
            }
            aria-label="Toggle sidebar"
            variant="ghost"
            size="sm"
            color="gray.600"
            _hover={{ bg: "gray.100" }}
            onClick={layoutUpdate.sidebarMobile}
            style={{ marginRight: 8, padding: 0, minWidth: 0, minHeight: 0, background: 'none', border: 'none' }}
            display={{ base: "flex", lg: "none" }}
            mr={0}
          />
        </span>
        <Text
          fontWeight="700"
          color="#222"
          fontSize={{ base: "14px", lg: "22px" }}
        >
          {academyName}
        </Text>
      </div>

      {/* Right: user dropdown */}
      <div>
        <QuickNav>
          <QuickNavItem>
            <Dropdown align="end">
              <Dropdown.Toggle
                as={CustomToggle}
                id="header-user-dropdown"
              >
                {isDesktop ? DesktopTrigger : MobileTrigger}
              </Dropdown.Toggle>

              <Dropdown.Menu
                as={CustomDropdownMenu}
                style={{
                  position: "absolute",
                  right: 0,
                  top: "100%",
                  borderRadius: 12,
                  border: "1px solid #e5e7eb",
                  boxShadow:
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                  marginTop: 8,
                  minWidth: 260,
                  backgroundColor: "#fff",
                  zIndex: 1000,
                }}
              >
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  textAlign="center"
                  py={2}
                  px={4}
                >
                  <div
                    style={{
                      fontWeight: 600,
                      marginBottom: 2,
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                    title={academyName}
                  >
                    {academyName}
                  </div>
                  <div
                    style={{
                      color: "#888",
                      fontWeight: 400,
                      fontSize: 13,
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                    title={userEmail}
                  >
                    {userEmail}
                  </div>
                </Box>

                <div style={{ padding: "2px 16px 16px" }}>
                  <LinkList>
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        gap: 12,
                        justifyContent: "center",
                      }}
                    >
                      <Button
                        colorScheme="red"
                        variant="ghost"
                        size="sm"
                        onClick={handleLogout}
                        leftIcon={
                          <Icon name="signout" style={{ fontSize: 18 }} />
                        }
                      >
                        Log Out
                      </Button>
                      <Button
                        colorScheme="gray"
                        variant="ghost"
                        size="sm"
                        onClick={handleForgetMe}
                        leftIcon={
                          <Icon name="signout" style={{ fontSize: 18 }} />
                        }
                      >
                        Forget me
                      </Button>
                    </div>
                  </LinkList>
                </div>
              </Dropdown.Menu>
            </Dropdown>
          </QuickNavItem>
        </QuickNav>
      </div>
    </div>
  );
}

export default Header;
