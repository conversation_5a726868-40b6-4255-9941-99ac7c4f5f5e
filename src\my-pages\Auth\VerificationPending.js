import React, { useEffect } from "react";
import {
  Box,
  Button,
  Center,
  Flex,
  Heading,
  Text,
} from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";
import { jwtDecode } from "jwt-decode";
import axios from "axios";

const VerificationPending = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const checkAuthStatus = async () => {
      let token = sessionStorage.getItem("admintoken")?.split(" ")[1];

      if (!token) {
        navigate("/login");
        return;
      }

      try {
        const decodedToken = jwtDecode(token);
        const academyId = decodedToken?.academyId?._id;
        if (academyId) {
          const response = await axios.get(
            `${process.env.REACT_APP_BASE_URL}/api/academy/${academyId}`,
            { headers: { Authorization: `Bearer ${token}` } }
          );

          const resData = response.data.data;
          if (resData?.accessToken) {
            sessionStorage.setItem("admintoken", `Bearer ${resData.accessToken}`);
            axios.defaults.headers.common["Authorization"] = `Bearer ${resData.accessToken}`;
          }

          if (resData?.refreshToken) {
            localStorage.setItem("refreshToken", resData.refreshToken);
          }
          if (resData?.status === "active") {
            navigate("/dashboard", { replace: true });
          }
        }
      } catch (error) {
        console.error("Error checking academy status:", error);
        if (error.response?.status === 401) {
          sessionStorage.removeItem("admintoken");
          localStorage.removeItem("refreshToken");
          navigate("/login");
        }
      }
    };

    checkAuthStatus();
  }, []);

  const handleLogout = () => {
    sessionStorage.removeItem("admintoken");
    localStorage.clear();
    navigate("/login");
  };

  return (
    <Flex
      minH="100vh"
      direction="column"
      justify="center"
      align="center"
      bg="gray.100"
      px={{ base: 4, sm: 0 }}
    >
      <Center
        flexDirection="column"
        w="100%"
        maxW={{ base: "md", md: "2xl", lg: "3xl" }}
        px={{ base: 0, sm: 0 }}
      >
        <Heading
          size="lg"
          mb={4}
          color="teal.600"
          textAlign="center"
        >
          Academy Verification Under Review
        </Heading>
        <Text
          fontSize={{ base: "md", sm: "lg" }}
          color="gray.700"
          mb={6}
          textAlign="center"
        >
          Thank you for registering! Your details are under review. You will be notified once your account is verified.
        </Text>
        <Button
          colorScheme="teal"
          w={{ base: "100%", sm: "auto" }}
          maxW="300px"
          onClick={handleLogout}
        >
          Logout
        </Button>
      </Center>
    </Flex>
  );
};

export default VerificationPending;