import React, { useState, useEffect } from "react";
import { Form, useFormik } from "formik";
import * as Yup from "yup";
import Layout from "../../layout/default";
import {
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Button,
  VStack,
  useToast,
  Card,
  CardBody,
  Box,
  Image,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Textarea,
  Spinner,
  Text,
  Tooltip,
} from "@chakra-ui/react";
import { Link, useNavigate, useParams } from "react-router-dom";
import axios from "axios";
import { IoMdArrowRoundBack } from "react-icons/io";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const CategoryEditForm = () => {
  const toast = useToast();
  const navigate = useNavigate();
  const { id } = useParams();

  const [image, setImage] = useState("");
  const [imagePrev, setImagePrev] = useState("");
  const [imagePrevView, setImagePrevView] = useState("");
  const [submitBtnLoading, setSubmitBtnLoading] = useState(false);
  const [isEdited, setIsEdited] = useState(false);
  const [categoryData, setCategoryData] = useState({
    result: { name: "", description: "", image: "", handle: "" },
    isLoading: false,
    error: false,
  });

  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];

  const getCategory = () => {
    setCategoryData({ ...categoryData, isLoading: true, error: false });
    axios
      .get(`${process.env.REACT_APP_BASE_URL}/api/category/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((response) => {
        setCategoryData({
          result: response.data,
          isLoading: false,
          error: false,
        });
        setImagePrev(response.data.image);
        setImagePrevView(response.data.image);
      })
      .catch((error) => {
        console.log(error);
        setCategoryData({ ...categoryData, isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  function restrictSpace(event) {
    setIsEdited(true);
    if (event.keyCode === 32) {
      event.preventDefault();
    }
  }

  useEffect(() => {
    getCategory();
  }, [id]);

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      name: categoryData.result.name,
      description: categoryData.result.description,
      image: imagePrev, // Set initial value of image field to empty string
      handle: categoryData.result.handle,
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().min(3).required("Name is required"),
      description: Yup.string().min(10),
      image: Yup.string().min(3).required(" Please select the image."),
      handle: Yup.string().min(3).required("Handle  is required"),
    }),
    onSubmit: async (values, { resetForm }) => {
      setSubmitBtnLoading(true);
      setIsEdited(false);
      let data = JSON.stringify(values);
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/category/${id}`,
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        data: data,
      };
      axios
        .request(config)
        .then((response) => {
          toast({
            title: "Category Updated Successfully.",
            status: "success",
            duration: 3500,
            isClosable: true,
            position: "top",
          });
          setSubmitBtnLoading(false);
          resetForm();
          setCategoryData({
            result: {
              name: response?.data?.name,
              description: response?.data?.description,
              image: response?.data?.image,
              handle: response?.data?.handle,
            },
            isLoading: false,
            error: false,
          });
          navigate(`/category-list/edit-category/${id}`);
        })
        .catch((error) => {
          console.log(error);
          setIsEdited(true);
          setSubmitBtnLoading(false);
          if (error.response.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });

  const handleImage = async (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();

    reader.readAsDataURL(file);
    reader.onloadend = () => {
      setImagePrev(reader.result);
      setImage(file);
    };

    const formData = new FormData();
    formData.append("image", file);
    formData.append("url", imagePrevView);
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/category/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const url = response.data.url;
      setImagePrevView(response.data.url);
      formik.setFieldValue("image", url);
      setIsEdited(true);
      toast({
        title: "You successfully updated your category photo.",
        status: "success",
        duration: 3500,
        isClosable: true,
        position: "top",
      });
    } catch (error) {
      setIsEdited(false);
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Edit Category" content="container">
        <Flex justifyContent={"flex-start"} alignItems={"center"} mb={6}>
          <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
          <Breadcrumb fontWeight="medium" fontSize="sm">

            <BreadcrumbItem>
              <Link to="/category-list">Categories</Link>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Update Category - #{id}</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
        </Flex>
        {Object.keys(categoryData?.result).length > 0 &&
        !categoryData?.isLoading ? (
          <Card mt={2}>
            <CardBody>
              <VStack spacing={4}>
                <form className="col-md-12" onSubmit={formik.handleSubmit}>
                  <fieldset>
                    <div className="col-md-12 row">
                      <div className="col-md-12 row mb-3">
                        <FormControl
                          isInvalid={formik.touched.name && formik.errors.name}
                        >
                          <FormLabel htmlFor="name">Name</FormLabel>
                          <Input
                            type="text"
                            id="name"
                            name="name"
                            onKeyDown={() => setIsEdited(true)}
                            onBlur={formik.handleBlur}
                            value={formik.values.name}
                            onChange={(e) => {
                              formik.setFieldValue(
                                "name",
                                e.target.value.replace(/[^a-zA-Z0-9\s]/g, "")
                              );
                              formik.setFieldValue(
                                "handle",
                                e.target.value
                                  .replace(/\s+/g, "-")
                                  .toLowerCase()
                              );
                            }}
                          />
                          <FormErrorMessage>
                            {formik.errors.name}
                          </FormErrorMessage>
                        </FormControl>
                      </div>

                      <div className="col-md-12 row mb-3">
                        <FormControl isInvalid={formik.errors.description}>
                          <FormLabel htmlFor="description">
                            Description
                          </FormLabel>
                          <Textarea
                            id="description"
                            name="description"
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            onKeyDown={() => setIsEdited(true)}
                            value={formik.values.description}
                          />
                          <FormErrorMessage>
                            {formik.errors.description}
                          </FormErrorMessage>
                        </FormControl> 
                      </div>

                      <div className="col-md-12 row mb-3">
                        <FormControl
                          isInvalid={
                            formik.touched.handle && formik.errors.handle
                          }
                        >
                          <FormLabel htmlFor="handle">Handle</FormLabel>
                          <Input
                            type="text"
                            id="handle"
                            name="handle"
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            value={formik.values.handle}
                            onKeyDown={restrictSpace}
                          />
                          <FormErrorMessage>
                            {formik.errors.handle}
                          </FormErrorMessage>
                        </FormControl>
                      </div>
                      <br />
                    </div>

                    <div className="col-md-12 row ">
                      <div className="col-md-12">
                        <FormControl
                          isInvalid={
                            formik.touched.image && formik.errors.image
                          }
                        >
                          <FormLabel htmlFor="image">Image</FormLabel>
                          <Input
                            type="file"
                            id="image"
                            name="image"
                            onChange={(e) => {
                              handleImage(e);
                              setIsEdited(false);
                            }}
                          />
                          <FormErrorMessage>
                            {formik.errors.image}
                          </FormErrorMessage>
                        </FormControl>
                      </div>
                    </div>

                    <div className="col-md-12 row">
                      <div className="col-md-6 center">
                        {imagePrev && (
                          <Box
                            mb={4}
                            mt={4}
                            w="70%"
                            borderWidth="1px"
                            borderRadius="lg"
                          >
                            <Image src={imagePrev} alt="loading" />
                          </Box>
                        )}
                      </div>
                    </div>
                  </fieldset>
                  <br />
                  <Button
                    colorScheme="telegram"
                    type="submit"
                    w={"100%"}
                    isLoading={submitBtnLoading}
                    isDisabled={!isEdited}
                  >
                    Update
                  </Button>
                </form>
              </VStack>
            </CardBody>
          </Card>
        ) : categoryData?.isLoading && !categoryData?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            mt={8}
            w={"full"}
          >
            <Spinner />
          </Flex>
        ) : (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              Something went wrong please try again later...
            </Text>
          </Flex>
        )}
      </Layout>
    </Box>
  );
};

export default CategoryEditForm;
