/* styles.css */

.react-datepicker__input-container input {
    width: 100%;
    padding: 8px 16px;
    font-size: 16px;
    border: 1.40px solid black;
    border-color: inherit;
    border-radius: 6px;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
    outline: 2px solid transparent;
  }

.react-datepicker__time-list-item.enabled-time {
  font-weight: 500 !important; 
  color: #000 !important;   
}

.react-datepicker__time-list-item.disabled-time {
  font-weight: 400 !important;
  color: #adabab !important;     
  cursor: not-allowed !important;
}
  .react-datepicker__input-container input:focus {
    z-index: 1;
    border-color: #3182ce;
    box-shadow: 0 0 0 1px #3182ce;
  }
  
  .react-datepicker__header {
    background-color: #f0f0f0;
    border-bottom: none;
  }
  
  .react-datepicker__month {
    margin: 0;
  }
  
  .react-datepicker__day-name,
  .react-datepicker__day,
  .react-datepicker__time-name {
    width: 30px;
    line-height: 30px;
    margin: 0;
    border-radius: 50%;
  }
  
  .react-datepicker__day--selected {
    background-color: #007bff;
    color: #fff;
  }
  
  .react-datepicker__day--keyboard-selected {
    background-color: #ccc;
  }

 
  
  /* Add specific style for dropdown */
/* Align popup under the input */
.react-datepicker-popper {
  inset: auto !important;
  left: 10% !important;
 transform: translateX(-50%) !important; 
  margin-top: 4px !important;
  width: auto !important; /* match input width */
}

/* Let the container stretch inside */
.react-datepicker__time-container {
  width: 100% !important;
  left:50% !important;
  border: 1px solid #ccc !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
  background-color: #fff !important;
  position: static !important; /* reset the wrong relative */
}


  /* Fix time list styling */
  .react-datepicker__time-list {
    border: none !important;
    border-radius: 6px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
  }

  /* Fix time list items */
  .react-datepicker__time-list-item {
    border-left: 1px solid #e2e8f0 !important;
    border-right: 1px solid #e2e8f0 !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    color: #0e131b !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-weight: 500 !important;
  }

  .react-datepicker__time-list-item:first-child {
    border-top: 1px solid #e2e8f0 !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }

  .react-datepicker__time-list-item:last-child {
    border-bottom: 1px solid #e2e8f0 !important;
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }

  .react-datepicker__time-list-item:hover {
    background-color: #f7fafc !important;
    border-color: #cbd5e0 !important;
  }

  .react-datepicker__time-list-item--selected {
    background-color: #3182ce !important;
    color: white !important;
    border-color: #3182ce !important;
  }

  .react-datepicker__time-list-item--selected:hover {
    background-color: #2c5aa0 !important;
    border-color: #2c5aa0 !important;
  }

  /* Add error style */
  .dateError {
    z-index: 1;
    border-color: #e53e3e;
    box-shadow: 0 0 0 1px #e53e3e;
  }