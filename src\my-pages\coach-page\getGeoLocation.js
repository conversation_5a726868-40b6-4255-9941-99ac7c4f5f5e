import axios from "axios";

export const getGeoLocations = async (address) => {
  const apiKey = "AIzaSyA0ZXZk4BWV0ScBgO0QLwd5mx7Yk7Zr96U";

  if (!apiKey) {
    throw new Error("Google Maps API key not configured");
  }

  try {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
      address
    )}&key=${apiKey}`;

    const response = await axios.get(url);
    if (response.data.status === "OK") {
      const result = response.data.results[0];
      const location = result.geometry.location;
      return {
        message: "Success",
        data: { Latitude: location.lat, Longitude: location.lng },
      };
    } else {
      console.error(
        "Geocode was not successful for the following reason:",
        response.data.status
      );
      if (response.data.error_message) {
        console.error("Error message:", response.data.error_message);
      }
      throw new Error(`Geocoding failed: ${response.data.status}`);
    }
  } catch (error) {
    console.error("There was an error fetching the geocode data:", error);
    throw error;
  }
};
