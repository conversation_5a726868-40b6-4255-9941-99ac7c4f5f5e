import axios from 'axios';

/**
 * Helper function to parse Google Maps Geocoding API response
 * @param {Object} response - Google Maps API response object
 * @returns {Object|null} - Parsed location data or null if parsing fails
 */
export const parseGoogleMapsResponse = (response) => {
  if (!response || !response.results || response.results.length === 0) {
    return null;
  }

  const result = response.results[0];
  const addressComponents = result.address_components;

  let city = "";
  let state = "";
  let country = "";

  // Parse address components to extract city, state, and country
  addressComponents.forEach(component => {
    const types = component.types;

    // Extract city (locality)
    if (types.includes("locality") && types.includes("political")) {
      city = component.long_name;
    }

    // Extract state (administrative_area_level_1)
    if (types.includes("administrative_area_level_1") && types.includes("political")) {
      state = component.long_name;
    }

    // Extract country
    if (types.includes("country") && types.includes("political")) {
      country = component.long_name;
    }
  });

  return {
    city: city,
    state: state,
    country: country,
    formatted_address: result.formatted_address
  };
};

/**
 * Fetch location details from Google Maps Geocoding API using pincode
 * @param {string} pincode - 6-digit pincode to lookup
 * @returns {Promise<Object>} - Promise that resolves to location data or throws error
 */
export const fetchLocationFromPincode = async (pincode) => {
  // Validate pincode
  if (!pincode || pincode.length !== 6 || !/^\d{6}$/.test(pincode)) {
    throw new Error('Invalid pincode format. Please enter a 6-digit pincode.');
  }

  // Check if API key is available
  if (!process.env.REACT_APP_API_KEY) {
    throw new Error('Google Maps API key not configured');
  }

  try {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${pincode}&key=${process.env.REACT_APP_API_KEY}`,
      { timeout: 10000 } // 10 second timeout
    );

    // Check if API response is valid
    if (response?.data?.status !== "OK" || !response?.data?.results?.length) {
      throw new Error('Invalid pincode or no results found');
    }

    // Parse the response
    const locationData = parseGoogleMapsResponse(response.data);
    
    if (!locationData) {
      throw new Error('Could not parse location data from API response');
    }

    return locationData;
  } catch (error) {
    // Re-throw with more specific error messages
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout. Please check your internet connection and try again.');
    } else if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      if (status === 403) {
        throw new Error('Google Maps API key is invalid or has insufficient permissions');
      } else if (status === 429) {
        throw new Error('API rate limit exceeded. Please try again later.');
      } else {
        throw new Error(`API request failed with status ${status}`);
      }
    } else if (error.request) {
      // Network error
      throw new Error('Network error. Please check your internet connection and try again.');
    } else {
      // Re-throw custom errors or unknown errors
      throw error;
    }
  }
};

/**
 * Utility function to get country code from country name
 * @param {string} countryName - Full country name
 * @returns {string} - Country code (defaults to full name if no mapping found)
 */
export const getCountryCode = (countryName) => {
  const countryMappings = {
    'India': 'IN',
    'United States': 'US',
    'United Kingdom': 'GB',
    'Canada': 'CA',
    'Australia': 'AU',
    // Add more mappings as needed
  };
  
  return countryMappings[countryName] || countryName;
};

/**
 * Utility function to find state ISO code from state name
 * @param {Array} statesArray - Array of state objects with name and isoCode properties
 * @param {string} stateName - Full state name to find
 * @returns {string} - State ISO code or empty string if not found
 */
export const findStateIsoCode = (statesArray, stateName) => {
  if (!statesArray || !Array.isArray(statesArray) || !stateName) {
    return '';
  }
  
  const stateData = statesArray.find(state => state.name === stateName);
  return stateData?.isoCode || '';
};

/**
 * Complete pincode lookup utility that handles the entire flow
 * @param {string} pincode - 6-digit pincode to lookup
 * @param {Object} options - Configuration options
 * @param {Array} options.statesArray - Array of state objects for ISO code mapping
 * @param {boolean} options.useCountryCode - Whether to return country code instead of full name
 * @param {boolean} options.useStateIsoCode - Whether to return state ISO code instead of full name
 * @returns {Promise<Object>} - Promise that resolves to formatted location data
 */
export const lookupPincodeLocation = async (pincode, options = {}) => {
  const {
    statesArray = [],
    useCountryCode = false,
    useStateIsoCode = false
  } = options;

  try {
    const locationData = await fetchLocationFromPincode(pincode);
    
    return {
      city: locationData.city,
      state: useStateIsoCode ? findStateIsoCode(statesArray, locationData.state) : locationData.state,
      country: useCountryCode ? getCountryCode(locationData.country) : locationData.country,
      formatted_address: locationData.formatted_address
    };
  } catch (error) {
    throw error;
  }
};
