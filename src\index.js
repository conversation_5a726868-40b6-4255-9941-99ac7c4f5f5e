import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import App from "./App";
import { ChakraProvider } from "@chakra-ui/react";
import { Provider } from "react-redux";
import { store, persistor } from "./store"; // Import both store and persistor
import { PersistGate } from 'redux-persist/integration/react';
import "./assets/scss/style.scss";
import { HelmetProvider } from "react-helmet-async";

import reportWebVitals from "./reportWebVitals";

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(

    <BrowserRouter>
      <ChakraProvider>
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <HelmetProvider>
              <App />
            </HelmetProvider>
          </PersistGate>
        </Provider>
      </ChakraProvider>
    </BrowserRouter>

);


reportWebVitals();
