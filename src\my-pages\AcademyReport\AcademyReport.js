import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import "react-datepicker/dist/react-datepicker.css";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { MdDownload } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  Tooltip,
  Badge,
  Select,
  InputGroup,
  InputRightAddon,
  Checkbox,

  useMediaQuery,
} from "@chakra-ui/react";
import axios from "axios";
import { useSelector } from "react-redux";
import ReactPaginate from "react-paginate";
import { Link, useNavigate } from "react-router-dom";
import moment from "moment-timezone";
import InvoicePrint from "./InvoicePrint";
import ReportMobileView from "./ReportMobile";
const Report = () => {
  const [contactData, setContactData] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });
  const [coachSearchDetails, setCoachSearchDetails] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const today = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");
  const [searchQueryCoach, setSearchQueryCoach] = useState("");
  const [searchCourseName, setSearchCourseName] = useState("");
  const [selectedClassType, setSelectedClassType] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [startDate, setStartDate] = useState(today);
  const [endDate, setEndDate] = useState(today);
  const [minEndDate, setMinEndDate] = useState(today);
  const [maxEndDate, setMaxEndDate] = useState(today);
  const [isSearched, setIsSearched] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedCoaches, setSelectedCoaches] = useState([]);
  const [isOpen2, setIsOpen2] = useState(false);
  const [isOpen3, setIsOpen3] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);
  const [newStatus, setNewStatus] = useState("");
  const onClose2 = () => setIsOpen2(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen2 = () => setIsOpen2(true);
  const onOpen3 = () => setIsOpen3(true);
  const [isOpen4, setIsOpen4] = useState(false);
  const onClose4 = () => {
    setIsOpen4(false);
    if (selectedCoaches.length > 0) {
      getCourseData(searchCourseName, selectedClassType, selectedStatus);
    }
  };
  const onOpen4 = () => setIsOpen4(true);
  const [isOpenWarning, setIsOpenWarning] = useState(false);
  const onCloseWarning = () => setIsOpenWarning(false);
  const toast = useToast();
  const navigate = useNavigate();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const [singleReport, setSingleReport] = useState({});
  const [invoiceModal, setInvoiceModal] = useState(false);
  const [singleReportIndex, setSingleReportIndex] = useState({});

  const checkDateRange = (start, end) => {
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 10;
  };

  const getCourseData = (searchCourseName, classType, status) => {
    setContactData({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });

    let queryString = `?page=${currentPage}`;
    queryString += `&isAcademy=true`;
    if (startDate) {
      queryString += `&startDate=${startDate}`;
    }
    if (endDate) {
      queryString += `&endDate=${endDate}`;
    }
    if (searchCourseName) {
      queryString += `&courseName=${searchCourseName}`;
    }
    if (status && status !== "all") {
      queryString += `&paymentStatus=${status}`;
    }
    if (classType) {
      queryString += `&userType=${classType}`;
    }
    if (selectedCoaches.length > 0) {
      selectedCoaches.forEach((coachId) => {
        queryString += `&coachId=${coachId}`;
      });
    }
    let config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/reports${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setContactData({
          result: response.data.report,
          isLoading: false,
          error: false,
          notFound: response.data.report.length === 0,
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        setContactData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleDownload = (searchCourseName, classType, status) => {
    let queryString = `?page=${currentPage}`;
    queryString += `&isAcademy=true`;
    if (startDate) {
      queryString += `&startDate=${startDate}`;
    }
    if (endDate) {
      queryString += `&endDate=${endDate}`;
    }
    if (searchCourseName) {
      queryString += `&courseName=${searchCourseName}`;
    }
    if (status) {
      queryString += `&paymentStatus=${status}`;
    }
    if (classType) {
      queryString += `&userType=${classType}`;
    }
    if (selectedCoaches.length > 0) {
      selectedCoaches.forEach((coachId) => {
        queryString += `&coachId=${coachId}`;
      });
    }
    let config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/downloadReports${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "report.csv");
        document.body.appendChild(link);
        link.click();
      })
      .catch((error) => {
        console.log(error);
        setContactData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };
  useEffect(() => {
    const start = moment(startDate).tz("Asia/Kolkata").startOf("day");
    const minEnd = start.clone();
    const maxEnd = moment.min(start.clone().add(7, "days"), moment(today));
    setMinEndDate(minEnd.format("YYYY-MM-DD"));
    setMaxEndDate(maxEnd.format("YYYY-MM-DD"));
    if (moment(endDate).isBefore(minEnd) || moment(endDate).isAfter(maxEnd)) {
      setEndDate(minEnd.format("YYYY-MM-DD"));
    }
  }, [startDate]);
  const getCoaches = async (name, page) => {
    let url = "";
    if (name) {
      url += `${process.env.REACT_APP_BASE_URL}/api/coach?firstName=${name}&status=active&authStatus=authorized`;
    } else {
      url += `${process.env.REACT_APP_BASE_URL}/api/coach?page=${page}&status=active&authStatus=authorized`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: url,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCoachSearchDetails({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
        if (response.data.data.length === 0) {
          setIsSearched(true);
        }
      })
      .catch((error) => {
        console.log(error);
        setCoachSearchDetails({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  const handleStatusChange = (contact, newStatus) => {
    setSelectedContact(contact);
    setNewStatus(newStatus);
    onOpen3();
  };

  const confirmStatusChange = () => {
    if (!selectedContact || !newStatus) return;
    let config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/markPaymentStatusPaid?classId=${selectedContact.classId}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then(() => {
        toast({
          title: "Status updated successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        getCourseData(searchCourseName, selectedClassType, selectedStatus);
      })
      .catch((error) => {
        console.log(error);
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .finally(() => {
        onClose3();
      });
  };
  const handleCoachSelect = (e, coachId) => {
    if (e.target.checked) {
      setSelectedCoaches([...selectedCoaches, coachId]);
    } else {
      setSelectedCoaches(selectedCoaches.filter((id) => id !== coachId));
    }
  };
  useEffect(() => {
    getCourseData(searchCourseName, selectedClassType, selectedStatus);
  }, [
    currentPage,
    searchCourseName,
    selectedClassType,
    selectedStatus,
    startDate,
    endDate,
    selectedCoaches,
  ]);
  useEffect(() => {
    getCoaches(searchQueryCoach, currentPage);
  }, [currentPage]);

  useEffect(() => {
    getCoaches("", 1);
  }, []);

  const [isMobile] = useMediaQuery("(max-width: 768px)");

  return (
    <Layout title="Contact">
      {/* Always render the coach selection modal so it works on both mobile and desktop */}
      <Modal isOpen={isOpen4} onClose={onClose4} size={isMobile ? "full" : "5xl"}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Select Coach</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Card mt={3} variant="outline" height={"95%"}>
              <CardBody>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search Coach"
                      value={searchQueryCoach}
                      onChange={(e) => {
                        setSearchQueryCoach(e.target.value);
                        setIsSearched(false);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          getCoaches(searchQueryCoach, 1);
                          setIsSearched(false);
                        }
                      }}
                    />
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => {
                        getCoaches(searchQueryCoach, 1);
                        setIsSearched(false);
                      }}
                      isDisabled={!(searchQueryCoach.length > 0)}
                    >
                      Search
                    </Button>
                  </Stack>
                  <Card mt={6}>
                    {!coachSearchDetails?.isLoading &&
                    coachSearchDetails?.error ? (
                      <Flex
                        justifyContent={"center"}
                        alignItems={"center"}
                        w={"full"}
                        my={10}
                      >
                        <Text color={"red.500"}>
                          Something went wrong please try again later...
                        </Text>
                      </Flex>
                    ) : (
                      <TableContainer
                        height={`${window.innerHeight - 300}px`}
                        overflowY={"scroll"}
                      >
                        <Table variant="simple">
                          <Thead
                            bgColor={"#c1eaee"}
                            position={"sticky"}
                            top={"0px"}
                            zIndex={"99"}
                          >
                            <Tr bgColor={"#E2DFDF"}>
                              <Th>S.No</Th>
                              <Th>Name</Th>
                              <Th>Gender</Th>
                              <Th>Experience</Th>
                              <Th>Email</Th>
                              <Th>Mobile</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            {coachSearchDetails?.isLoading &&
                            !coachSearchDetails?.error ? (
                              <Tr>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                                <Td
                                  display={"flex"}
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                >
                                  <Spinner />
                                </Td>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                              </Tr>
                            ) : !coachSearchDetails?.isLoading &&
                              coachSearchDetails?.error ? (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                                w={"full"}
                                my={10}
                              >
                                <Text color={"red.500"}>
                                  Something went wrong please try again later...
                                </Text>
                              </Flex>
                            ) : !isSearched ? (
                              coachSearchDetails?.result?.map((coach, i) => {
                                return (
                                  <Tr key={coach._id}>
                                    <Td>
                                      <Checkbox
                                        isChecked={selectedCoaches.includes(
                                          coach._id
                                        )}
                                        onChange={(e) =>
                                          handleCoachSelect(e, coach._id)
                                        }
                                      />
                                    </Td>
                                    <Td
                                      style={{
                                        whiteSpace: "pre-wrap",
                                        wordWrap: "break-word",
                                      }}
                                      fontSize={"14px"}
                                    >
                                      {coach?.firstName + " " + coach?.lastName}
                                    </Td>
                                    <Td fontSize={"14px"}>
                                      {coach?.gender?.toUpperCase() || "n/a"}
                                    </Td>
                                    <Td textAlign={"center"} fontSize={"14px"}>
                                      {coach?.experience
                                        ? coach?.experience + " years"
                                        : "n/a"}
                                    </Td>
                                    <Td fontSize={"14px"}>{coach?.email}</Td>
                                    <Td fontSize={"14px"}>{coach?.mobile}</Td>
                                  </Tr>
                                );
                              })
                            ) : (
                              <Tr>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
       <Td
                          display={"flex"}
                          justifyContent={"center"}
                          alignItems={"center"}
                        >
                          <Text color={"green.500"} fontWeight={"semibold"}>
                            No result found
                          </Text>
                        </Td>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                              </Tr>
                            )}
                          </Tbody>
                        </Table>
                      </TableContainer>
                    )}
                  </Card>
                </Box>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* End always-on coach selection modal */}
      {isMobile ? (
        <ReportMobileView
          data={contactData.result}
          isLoading={contactData.isLoading}
          startDate={startDate}
          endDate={endDate}
          minEndDate={minEndDate}
          maxEndDate={maxEndDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          searchCourseName={searchCourseName}
          setSearchCourseName={setSearchCourseName}
          selectedStatus={selectedStatus}
          setSelectedStatus={setSelectedStatus}
          onSearch={() => getCourseData(searchCourseName, selectedClassType, selectedStatus)}
          onDownload={handleDownload}
          onCoachSearch={onOpen4}
          setSingleReport={setSingleReport}
          setSingleReportIndex={setSingleReportIndex}
          setInvoiceModal={setInvoiceModal}
          totalPages={totalPages}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
          onInvoiceDownload={(report) => {
            setSingleReport(report);
            setInvoiceModal(true);
            setSingleReportIndex(contactData.result.findIndex(r => r.bookingId === report.bookingId));
          }}
          onStatusChange={handleStatusChange}
        />
      ) : (
        <>
          {/* Desktop UI: filters, table, pagination, modals, etc. */}
          <Flex
            w={"100%"}
            justifyContent={"space-between"}
            alignItems={"center"}
            mb={6}
          >
            {showSearch ? (
              <Box flexBasis={"58%"}>
                <InputGroup size="md">
                  <Input
                    pr="4.5rem"
                    type="text"
                    placeholder="Search"
                    borderColor={"gray.300"}
                    onChange={(e) => {
                      if (e.target.value.length >= 3) {
                        setTimeout(() => {
                          setSearchCourseName(e.target.value);
                        }, 500);
                      }
                      if (e.target.value.length === 0) {
                        setSearchCourseName("");
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        if (e.target.value.length >= 3) {
                          setSearchCourseName(e.target.value);
                        }
                      }
                    }}
                  />
                  <InputRightAddon
                    bgColor={"gray.300"}
                    border={"1px"}
                    borderColor={"gray.300"}
                    onClick={() => {
                      setShowSearch(false);
                      setSearchCourseName("");
                    }}
                    cursor={"pointer"}
                  >
                    <IoIosClose fontSize={"24px"} />
                  </InputRightAddon>
                </InputGroup>
              </Box>
            ) : (
              <Flex
                flexBasis={"59%"}
                justifyContent={"space-between"}
                alignItems={"center"}
              >
                    <Flex alignItems="center" gap={0} mb={{ base: 3, md: 0 }}>
                      <Button
                        variant="ghost"
                        size={{ base: "xs", md: "sm" }}
                        leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
                        onClick={() => navigate(-1)}
                        _hover={{ bg: "gray.100" }}
                        color="gray.700"
                        fontWeight="bold"
                        className="p-0"
                      >
                      </Button>
                      <Breadcrumb fontWeight="medium" fontSize="18px">
                        <BreadcrumbItem isCurrentPage>
                          <BreadcrumbLink href="#">Reports</BreadcrumbLink>
                        </BreadcrumbItem>
                      </Breadcrumb>
                    </Flex>

                <Box
                  flexBasis={"80%"}
                  display="flex"
                  flexDirection="row"
                  zIndex={20}
                  justifyContent="space-evenly"
                  textAlign={"center"}
                  alignItems={"center"}
                >
                  Choose Date
                  <input
                    type="date"
                    name="start_date"
                    value={startDate}
                    id="start_date"
                    max={today}
                    onChange={(e) => {
                      setCurrentPage(1);
                      setStartDate(e.target.value);
                    }}
                  />
                  <input
                    type="date"
                    name="end_date"
                    value={endDate}
                    id="end_date"
                    min={minEndDate}
                    max={maxEndDate}
                    onChange={(e) => {
                      setCurrentPage(1);
                      setEndDate(e.target.value);
                    }}
                  />
                </Box>
              </Flex>
            )}
            <Flex flexBasis={"40%"} justifyContent={"space-evenly"}>
              <Box flexBasis={"31%"}>
                <Select
           
                  borderColor={"gray.300"}
                  cursor={"pointer"}
                  bgColor={selectedStatus && "gray.300"}
                  onChange={(e) => {
                    setSelectedStatus(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="" disabled>Status</option>
                  <option value="all">All</option>
                  <option value="paid">Paid</option>
                  <option value="unpaid">Unpaid</option>
                </Select>
              </Box>

              <Box flexBasis={"31%"}>
                <Button
                  variant={"outline"}
                  colorScheme="teal"
                  size={"sm"}
                  px={4}
                  isDisabled={!userData?.accessScopes?.course?.includes("write")}
                  onClick={() => {
                    onOpen4();
                    setIsSearched(false);
                    setSearchQueryCoach("");
                    getCoaches("", 1);
                  }}
                  width={"100%"}
                  height={"100%"}
                >
                  Search By Coach Name
                </Button>
              </Box>
            </Flex>
            <Button onClick={handleDownload}>Download</Button>
          </Flex>



          {/* Added/Selected Course List   total amount Received , course fee normal , course gst(coach), UMN fee(platform fee), UMN gst(), refund, */}
          {!contactData?.isLoading && contactData?.error ? (
            <Flex
              justifyContent={"center"}
              alignItems={"center"}
              w={"full"}
              my={10}
            >
              <Text color={"red.500"}>
                Something went wrong please try again later...
              </Text>
            </Flex>
          ) : (
            <Box width="100%">
              {contactData.result.length > 0 ? (
                // When there is data, headings and values scroll together
                <Box overflowX="auto">
                  <Table minWidth="900px" width="100%">
                    <Thead bgColor="#c1eaee" position="sticky" top="0px" zIndex="2">
                      <Tr bgColor="#E2DFDF">
                        <Th>Booking Id</Th>
                        <Th>Invoice</Th>
                        <Th>Date</Th>
                        <Th>Training Schedule Name</Th>
                        <Th>Course fees </Th>
                        <Th>Course GST</Th>
                        <Th>Course fee with GST</Th>
                        <Th>Refunded Amount</Th>
                        <Th>TCS</Th>
                        <Th>Amount due to academy</Th>
                        <Th>Status</Th>
                        <Th>Coach attendance</Th>
                        <Th>Player attendance</Th>
                        <Th>Invoice</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {contactData.result.map((report, inx) => (
                        <Tr key={inx}>
                          <Td>{report?.bookingId}</Td>
                          <Td>{report?.invoice}/{report?.classIndex + 1}</Td>
                          <Td>{report?.date?.split("T")[0]}</Td>
                          <Td>
                            {report?.courseName.split(" ").slice(0, 4).join(" ")}
                          </Td>
                      
                          <Td>
                           {report?.courseFee.toFixed(2)}
                          </Td>
                          <Td>{report?.courseGst.toFixed(2)}</Td>

                          <Td>
                            {(report?.courseFee + report?.courseGst).toFixed(2)}
                          </Td>
                          <Td>
                            {report?.refundAmount.toFixed(2)}
                          </Td>

                          <Td>
                          {report?.tcs.toFixed(2)}
                          </Td>

                          {/* <Td>
                            {report?.hasGst
                              ? (
                                  parseFloat(report?.classFees?.toFixed(2)) * 0.18 ||
                                  0
                                ).toFixed(2)
                              : "N/A"}
                          </Td> */}
                          <Td> {(report?.amountDueToAcademy).toFixed(2)}</Td>
                 
                          <Td>
                            <Select
                              size="lg"
                              width="100px"
                              defaultValue={report?.paymentStatus}
                              onChange={(e) =>
                                handleStatusChange(report, e.target.value)
                              }
                              bgColor={
                                report?.paymentStatus === "paid"
                                  ? "green.100"
                                  : "red.100"
                              }
                              isDisabled={report?.paymentStatus === "paid"}
                            >
                              <option value="paid" style={{ color: "green" }}>
                                Paid
                              </option>
                              <option value="unpaid" style={{ color: "red" }}>
                                Unpaid
                              </option>
                            </Select>
                          </Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            px={4}
                            fontSize={"14px"}
                          >
                            <Badge
                              colorScheme={
                                report?.coachAttendance === "present"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {report?.coachAttendance}
                            </Badge>
                          </Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            px={4}
                            fontSize={"14px"}
                          >
                            <Badge
                              colorScheme={
                                report?.playerAttendance === "present"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {report?.playerAttendance}
                            </Badge>
                          </Td>
                          <Td>
                            <Button
                              leftIcon={<MdDownload />}
                              colorScheme="blue"
                              variant="link"
                              onClick={() => {
                                setSingleReport(report);
                                setInvoiceModal(true);
                                setSingleReportIndex(inx);
                              }}
                            >
                              Invoice
                            </Button>
                          </Td>

                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>
              ) : (
                // When no data, headings can scroll independently
                <>
                  <Box overflowX="auto">
                    <Table minWidth="900px" width="100%">
                      <Thead bgColor="#c1eaee" position="sticky" top="0px" zIndex="2">
                        <Tr bgColor="#E2DFDF">
                          <Th>Booking Id</Th>
                          <Th>Date</Th>
                          <Th>Training Schedule Name</Th>
                          <Th>Course Fee</Th>
                          <Th>GST</Th>
                          <Th>Course fees with GST</Th>
                          <Th>Refunded Amount (is any)</Th>
                          <Th>TCS</Th>
                          <Th>Amount due to academy</Th>
                          <Th>Status</Th>
                          <Th>Coach attendance</Th>
                          <Th>Player attendance</Th>
                          <Th>Invoice</Th>
                   
                        </Tr>
                      </Thead>
                    </Table>
                  </Box>
                  <Box width="100%">
                    <Table minWidth="900px" width="100%">
                      <Tbody>
                        <Tr>
                          <Td colSpan={16} style={{ padding: 0 }}>
                            <Box
                              width="100%"
                              minH="200px"
                              display="flex"
                              alignItems="center"
                              justifyContent="center"
                            >
                              <Text color="green.500" fontWeight="semibold" textAlign="center" width="100%">
                                No result found
                              </Text>
                            </Box>
                          </Td>
                        </Tr>
                      </Tbody>
                    </Table>
                  </Box>
                </>
              )}
            </Box>
          )}
          {contactData.result.length > 0 && (
            <Flex justifyContent="flex-end" mt={4}>
              <ReactPaginate
                previousLabel={"Previous"}
                nextLabel={"Next"}
                breakLabel={"..."}
                breakClassName={"break-me"}
                pageCount={totalPages}
                marginPagesDisplayed={2}
                pageRangeDisplayed={5}
                onPageChange={handlePageChange}
                containerClassName={"pagination"}
                activeClassName={"active"}
                forcePage={currentPage - 1}
              />
            </Flex>
          )}
        </>
      )}

      {/* Modals - Available for both mobile and desktop */}
      {invoiceModal && (
        <InvoicePrint
          invoiceModal={invoiceModal}
          setInvoiceModal={setInvoiceModal}
          singleReport={singleReport}
          singleReportIndex={singleReportIndex}
        />
      )}

      <Modal isOpen={isOpenWarning} onClose={onCloseWarning}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Warning</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>The selected date range should not exceed 10 days.</Text>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" onClick={onCloseWarning}>
              OK
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <Modal isOpen={isOpen3} onClose={onClose3}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Update Status</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            Is this query is resolved, if YES then make it to inactive.
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="red" mr={3} onClick={onClose3}>
              Cancel
            </Button>
            <Button colorScheme="green" onClick={confirmStatusChange}>
              Save Changes
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Layout>
  );
};

export default Report;
