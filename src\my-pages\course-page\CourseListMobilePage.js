import {
  Box,
  Text,
  Button,
  Badge,
  Flex,
  Input,
  Select,
  Card,
  CardBody,
  Stack,
  InputGroup,
  InputRightAddon,
  Tooltip,
} from "@chakra-ui/react";
import { FaCircleArrowRight } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { IoIosClose } from "react-icons/io";
import ReactPaginate from "react-paginate";
import { Link } from "react-router-dom";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@chakra-ui/react";
import { IoArrowBackCircleOutline } from "react-icons/io5";

export default function CourseListMobileView({
  data,
  isLoading,
  searchCourseName,
  setSearchCourseName,
  selectedClassType,
  setSelectedClassType,
  selectedStatus,
  setSelectedStatus,
  handleCreateCourse,
  userData,
  totalPages,
  currentPage,
  handlePageChange,
  notFound,
}) {
  const navigate = useNavigate();

  return (
    <Box px={{ base: 0, md: 1 }} py={2}>
      <Flex alignItems="center" gap={0} mb={6}>
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
          className="p-0"
        >
        </Button>
        <Breadcrumb fontWeight="medium" fontSize="18px">
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Training Schedule</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
      </Flex>
      {/* Search */}
      <InputGroup mb={3}>
        <Input
          placeholder="Search"
          value={searchCourseName}
          onChange={(e) => setSearchCourseName(e.target.value)}
        />
        {searchCourseName && (
          <InputRightAddon
            bgColor={"gray.300"}
            border={"1px"}
            borderColor={"gray.300"}
            onClick={() => setSearchCourseName("")}
            cursor={"pointer"}
          >
            <IoIosClose fontSize={"24px"} />
          </InputRightAddon>
        )}
      </InputGroup>

      {/* Filters and Create Button */}
      <Flex gap={2} mb={4} direction="row" flexWrap="nowrap">
        <Select
     
          value={selectedClassType}
          onChange={(e) => setSelectedClassType(e.target.value)}
          flex="1"
          minW="0"
        >
          <option value="" disabled>Class Type</option>
          <option value="all">All</option>
          <option value="class">Class</option>
          <option value="course">Course</option>
        </Select>
        <Select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          flex="1"
          minW="0"
        >
          <option value="" disabled>Status</option>
          <option value="all">All</option>
          <option value="active">Active</option>
          <option value="inactive">In-Active</option>
        </Select>
        {userData?.accessScopes?.course?.includes("write") && (
          <Button 
            onClick={handleCreateCourse} 
            colorScheme="teal" 
            variant="outline" 
            flex="1"
            minW="100px"
            whiteSpace="normal"
            fontSize="12px"
          >
            Create Training Schedule
          </Button>
        )}
      </Flex>

      {/* Card List */}
      <Stack spacing={4}>
        {data && data.length > 0 ? (
          data.map((course) => (
            <Card key={course._id} border="1px solid #CBD5E0">
              <CardBody>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Training Schedule Name:</Text>
                  <Text fontSize="sm" fontWeight="normal">{course.courseName || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Coach Name:</Text>
                  <Text fontSize="sm" fontWeight="normal">{course.coachName || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Type:</Text>
                  <Text fontSize="sm" fontWeight="normal">{course?.classType?.charAt(0)?.toUpperCase() + course?.classType?.slice(1) || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Status:</Text>
                  <Badge colorScheme={course.status === "active" ? "green" : "red"} fontSize="xs">{course.status?.toUpperCase()}</Badge>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Category:</Text>
                  <Text fontSize="sm" fontWeight="normal">{course.category || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Enrolled:</Text>
                  <Text fontSize="sm" fontWeight="normal">{course.playerEnrolled || "0"}</Text>
                </Flex>
                {userData?.accessScopes?.course?.includes("read") && (
                  <Flex justify="flex-end" mt={3}>
                    <Button
                      size="sm"
                      rightIcon={<FaCircleArrowRight />}
                      onClick={() =>
                        navigate(`/course-page/details/${course._id}`)
                      }
                    >
                      View Details
                    </Button>
                  </Flex>
                )}
              </CardBody>
            </Card>
          ))
        ) : (
          <Text color="gray.500" textAlign="center" py={6}>
            No course details to show
          </Text>
        )}
      </Stack>

      {/* Pagination */}
      {!notFound && (
        <Flex
          justifyContent="center"
          alignItems="center"
          flexDirection={"row"}
          w={"100%"}
          mt={5}
        >
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
            forcePage={currentPage - 1}
          />
        </Flex>
      )}
    </Box>
  );
}
