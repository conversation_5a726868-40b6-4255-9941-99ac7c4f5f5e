import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  Button,
  ModalCloseButton,
  Box,
  Flex,
  Heading,
  Text,
  VStack,
  useBreakpointValue
} from "@chakra-ui/react";
import React, { useRef } from "react";
import { MdLocalPrintshop } from "react-icons/md";
import { useReactToPrint } from "react-to-print";
import { numberToWords } from "../../utilities/convertIntoWords";

const InvoicePrint = ({
  singleReport,
  invoiceModal,
  setInvoiceModal,
  singleReportIndex
}) => {
  const invoiceRef = useRef();
  const handlePrint = useReactToPrint({
    content: () => invoiceRef.current,
  });

  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  function convertDateIntoIndianFormat(date) {
    if (!date) return "";
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  }

  const modalWidth = useBreakpointValue({ base: "98%", sm: "95%", md: "80%", lg: "70%" });
  const boxPadding = useBreakpointValue({ base: 2, sm: 4, md: 8, lg: 10 });
  const headingSize = useBreakpointValue({ base: "md", md: "lg" });
  const fontSizeSm = useBreakpointValue({ base: "xs", sm: "sm" });
  const vStackSpacing = useBreakpointValue({ base: 0.5, sm: 1 });

  // Calculate values safely with fallbacks
  const courseFee =
    (singleReport?.academyGst > 0
      ? singleReport?.coachFeesAfterCancelation / 1.18
      : singleReport?.coachFeesAfterCancelation) || 0;
  const courseGst =
    singleReport?.academyGst > 0 ? courseFee * 0.18 : 0;
  const tcs = singleReport?.tcs || 0;
  const totalAmount = (courseFee + courseGst - tcs).toFixed(2);

  const handleClose = () => {
    setInvoiceModal(false);
  };

  return (
    <Modal
      isOpen={invoiceModal}
      onClose={handleClose}
      size="full"
      closeOnOverlayClick={true}
    >
      <ModalOverlay />
      <ModalContent maxW={modalWidth} w="100%">
        {/* Add ModalCloseButton here */}
        <ModalCloseButton />

        <ModalBody p={boxPadding}>
          <Box p={boxPadding} ref={invoiceRef}>
            <Heading as="h1" size={headingSize} mb={4}>
              Academy Invoice
            </Heading>

            <Flex
              direction={{ base: "column", md: "row" }}
              justifyContent="space-between"
              my={{ base: 4, md: 10 }}
              gap={4}
            >
              <VStack align="start" spacing={vStackSpacing} w={{ base: "100%", md: "50%" }}>
                <Text fontWeight="bold" fontSize={fontSizeSm}>
                  From,
                </Text>
                <Text fontWeight="bold" fontSize={fontSizeSm}>
                  {singleReport?.academyName || "N/A"}
                </Text>
                <Text fontSize={fontSizeSm}>
                  {singleReport?.officeAddress?.addressLine1 || ""},
                  {singleReport?.officeAddress?.city || ""}
                </Text>
                <Text fontSize={fontSizeSm}>
                  {singleReport?.officeAddress?.state || ""},
                  {singleReport?.officeAddress?.pinCode || ""}
                </Text>
                <Text fontSize={fontSizeSm} mt={2}>
                  GST ID: {singleReport?.gstNumber || "N/A"}
                </Text>
              </VStack>

              <VStack align="start" spacing={vStackSpacing} w={{ base: "100%", md: "50%" }}>
                <Flex>
                  <Text fontSize={fontSizeSm} fontWeight="bold">
                    Date:
                  </Text>
                  <Text fontSize={fontSizeSm} ml={2}>
                    {singleReport?.date ?
                      convertDateIntoIndianFormat(new Date(singleReport.date.split("T")[0])) :
                      "N/A"
                    }
                  </Text>
                </Flex>
                <Flex>
                  <Text fontSize={fontSizeSm} fontWeight="bold">
                    Invoice No.:
                  </Text>
                  <Text fontSize={fontSizeSm} ml={2}>
                    {singleReport?.invoice || "N/A"}/{singleReport?.classIndex + 1}
                  </Text>
                </Flex>
              </VStack>
            </Flex>

            <Box mb={{ base: 4, md: 10 }}>
              <Flex justifyContent="space-between" direction={{ base: "column", sm: "row" }} gap={2}>
                <Text fontSize={fontSizeSm} fontWeight="light" textDecoration="underline">
                  DESCRIPTION
                </Text>
                <Text fontSize={fontSizeSm} fontWeight="light" textDecoration="underline">
                  AMOUNT
                </Text>
              </Flex>

              <Box mt={2}>
                <Flex justifyContent="space-between" direction={{ base: "column", sm: "row" }} gap={2}>
                  <Text fontSize={fontSizeSm}> {singleReport?.courseName || "N/A"}</Text>
                  <Text fontSize={fontSizeSm}></Text>
                </Flex>
                <Flex justifyContent="space-between" direction={{ base: "column", sm: "row" }} gap={2}>
                  <Text fontSize={fontSizeSm}>Invoice Amount (exclusive of GST)</Text>
                  <Text fontSize={fontSizeSm}>₹{courseFee.toFixed(2)}</Text>
                </Flex>
                {singleReport?.academyGst > 0 && (
                  <>
                    <Flex justifyContent="space-between" direction={{ base: "column", sm: "row" }} gap={2}>
                      <Text fontSize={fontSizeSm}>GST</Text>
                      <Text fontSize={fontSizeSm}>₹{courseGst.toFixed(2)}</Text>
                    </Flex>

                    <Flex justifyContent="space-between" direction={{ base: "column", sm: "row" }} gap={2}>
                      <Text fontSize={fontSizeSm}>Total Invoice</Text>
                      <Text fontSize={fontSizeSm}>₹{(courseFee + courseGst).toFixed(2)}</Text>
                    </Flex>


                    <Flex justifyContent="space-between" direction={{ base: "column", sm: "row" }} gap={2}>
                      <Text fontSize={fontSizeSm}>TCS (1% on ₹{courseFee.toFixed(2)})</Text>
                      <Text fontSize={fontSizeSm}>₹{tcs.toFixed(2)}</Text>
                    </Flex>
                  </>
                )}
                <Flex justifyContent="space-between" direction={{ base: "column", sm: "row" }} gap={2} mt={2}>
                  <Text fontSize={fontSizeSm} fontWeight="bold">
                    Amount Due to Academy:
                  </Text>
                  <Text fontSize={fontSizeSm} fontWeight="bold">
                    ₹{totalAmount}
                  </Text>
                </Flex>
              </Box>
            </Box>

            <Box mb={{ base: 4, md: 10 }}>
              <Text fontSize={fontSizeSm}>
                Amount (in words): Rupees {numberToWords(totalAmount)} only.
              </Text>
            </Box>

            <Box mb={{ base: 4, md: 10 }}>
              <Text fontSize={fontSizeSm} fontWeight="bold">
                Training Details:
              </Text>
              <Text fontSize={fontSizeSm}>
                Booking ID: {singleReport?.bookingId || "N/A"}<br />
                Date: {singleReport?.date ? singleReport.date.split("T")[0] : "N/A"}<br />
                Training Schedule: {singleReport?.courseName || "N/A"}<br />
                Coach Attendance: {singleReport?.coachAttendance || "N/A"}<br />
                Player Attendance: {singleReport?.playerAttendance || "N/A"}
              </Text>
            </Box>

            <Box mb={{ base: 4, md: 10 }}>
              <Text fontSize={fontSizeSm}>
                Regards,<br />
                UMN Khel Shiksha Private Limited
              </Text>
            </Box>
          </Box>
        </ModalBody>

        <ModalFooter flexDirection={{ base: "column", sm: "row" }} gap={4}>


          <Button
            leftIcon={<MdLocalPrintshop />}
            colorScheme="blue"
            variant="link"
            onClick={handlePrint}
            w={{ base: "100%", sm: "auto" }}
          >
            Print
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default InvoicePrint;