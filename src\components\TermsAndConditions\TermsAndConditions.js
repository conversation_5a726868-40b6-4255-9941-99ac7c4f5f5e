"use client";

import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import axios from "axios";
import {
  Box,
  Flex,
  <PERSON>ing,
  <PERSON><PERSON>,
  Spinner,
  Checkbox,
} from "@chakra-ui/react";

const TermsAndConditionModal = ({ open, setOpen, saveData }) => {
  const [policy, setPolicy] = useState("");
  const [agreed, setAgreed] = useState(false);

  const getPolicy = async () => {
    try {
      const data = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/cms/cms-academy-termsAndCondition-details`
      );
      setPolicy(data?.data[0]?.termsAndConditionData || "");
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (open) {
      getPolicy();
    }
  }, [open]);

  if (!open) return null;

  const modalContent = (
    <Box
      position="fixed"
      top={0}
      right={0}
      bottom={0}
      left={0}
      zIndex="9999"
      display="flex"
      alignItems="center"
      justifyContent="center"
      p={4}
    >
      {/* Overlay */}
      <Box
        position="absolute"
        top={0}
        right={0}
        bottom={0}
        left={0}
        bg="blackAlpha.600"
        backdropFilter="blur(4px)"
        onClick={() => setOpen(false)}
      />

      {/* Modal content */}
      <Box
        position="relative"
        bg="white"
        maxW="4xl"
        w="full"
        maxH="85vh"
        borderRadius="xl"
        boxShadow="2xl"
        zIndex={10}
        overflow="hidden"
      >
        {/* Header */}
        <Box bg="gray.200" px={6} py={4}>
          <Flex align="center" justify="space-between">
            <Heading as="h2" size="lg" fontWeight="bold" color="gray.700">
              Terms & Conditions
            </Heading>
            <Button
              onClick={() => setOpen(false)}
              variant="ghost"
              color="gray.700"
              _hover={{ color: "gray.500", bg: "transparent" }}
            >
              ✕
            </Button>
          </Flex>
        </Box>

        {/* Content */}
        <Box p={6} overflowY="auto" maxH="calc(85vh - 160px)">
          {policy ? (
            <Box
              as="div"
              fontSize="sm"
              color="gray.700"
              lineHeight="tall"
              dangerouslySetInnerHTML={{ __html: policy }}
            />
          ) : (
            <Flex align="center" justify="center" py={8}>
              <Spinner thickness="2px" size="lg" />
              <Box ml={3} color="gray.600">
                Loading terms and conditions...
              </Box>
            </Flex>
          )}
        </Box>

        {/* Footer */}
        <Box bg="gray.50" px={6} py={4} borderTopWidth="1px" borderColor="gray.200">
          <Flex justify="space-between" align="center" gap={4}>
            <Checkbox
              isChecked={agreed}
              onChange={(e) => setAgreed(e.target.checked)}
              colorScheme="green"
            >
              I have read and agree to the Terms & Conditions
            </Checkbox>
            <Button
              px={8}
              py={2}
              borderRadius="lg"
              bg="green.600"
              color="white"
              _hover={{ bg: "green.700" }}
              fontWeight="medium"
              boxShadow="lg"
              onClick={() => {
                saveData.handleSubmit();
                setOpen(false);
              }}
              isDisabled={!agreed}
            >
              I Agree & Register
            </Button>
          </Flex>
        </Box>
      </Box>
    </Box>
  );

  return createPortal(modalContent, document.body);
};

export default TermsAndConditionModal;
