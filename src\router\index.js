import React from "react";
import { Routes, Route } from "react-router-dom";
import { Navigate, useLocation } from "react-router-dom";
import { jwtDecode } from "jwt-decode";

import { ScrollToTop } from "../components";

//Pages
import Blank from "../pages/Blank";
import HomeEcommerce from "../pages/HomeEcommerce";
import HomeProject from "../pages/HomeProject";
import HomeMarketing from "../pages/HomeMarketing";
import HomeNFT from "../pages/HomeNFT";

// apps
import AppCalendar from "../pages/AppCalendar";
import KanbanBasic from "../pages/kanban/KanbanBasic";
import KanbanCustom from "../pages/kanban/KanbanCustom";
import Chats from "../pages/apps/chat/Chats";
import Inbox from "../pages/apps/mailbox/Inbox";

// user manage
import UserList from "../pages/user-manage/UserList";
import UserCards from "../pages/user-manage/UserCards";
import UserProfile from "../pages/user-manage/UserProfile";
import UserEdit from "../pages/user-manage/UserEdit";

// admin
import Profile from "../pages/admin/Profile";
import ProfileSettings from "../pages/admin/ProfileSettings";

// ecommerce
import Products from "../pages/ecommerce/Products";
import AddProduct from "../pages/ecommerce/AddProduct";
import EditProduct from "../pages/ecommerce/EditProduct";

// ui elements
import Accordion from "../pages/components/Accordion";
import Alert from "../pages/components/Alert";
import Badge from "../pages/components/Badge";
import Breadcrumb from "../pages/components/Breadcrumb";
import Buttons from "../pages/components/Buttons";
import ButtonGroup from "../pages/components/ButtonGroup";
import Cards from "../pages/components/Cards";
import Carousel from "../pages/components/Carousel";
import CloseButton from "../pages/components/CloseButton";
import Collapse from "../pages/components/Collapse";
import Dropdowns from "../pages/components/Dropdowns";
import ListGroup from "../pages/components/ListGroup";
import Modal from "../pages/components/Modal";
import Tabs from "../pages/components/Tabs";
import Offcanvas from "../pages/components/Offcanvas";
import Pagination from "../pages/components/Pagination";
import Placeholders from "../pages/components/Placeholders";
import Popovers from "../pages/components/Popovers";
import Progress from "../pages/components/Progress";
import Spinners from "../pages/components/Spinners";
import Toasts from "../pages/components/Toasts";
import Tooltips from "../pages/components/Tooltips";

// utilities
import Misc from "../pages/utilities/Misc";
import Typography from "../pages/utilities/Typography";
import Images from "../pages/utilities/Images";
import Tables from "../pages/utilities/Tables";
import Background from "../pages/utilities/Background";
import Borders from "../pages/utilities/Borders";
import Colors from "../pages/utilities/Colors";
import Flex from "../pages/utilities/Flex";
import Sizing from "../pages/utilities/Sizing";
import Spacing from "../pages/utilities/Spacing";

// layout
import Breakpoints from "../pages/layout/Breakpoints";
import Containers from "../pages/layout/Containers";
import Gutters from "../pages/layout/Gutters";
// fomrs
import FormControl from "../pages/forms/FormControl";
import FormSelect from "../pages/forms/FormSelect";
import DateTime from "../pages/forms/DateTime";
import FormUpload from "../pages/forms/FormUpload";
import InputGroup from "../pages/forms/InputGroup";
import FloatingLabels from "../pages/forms/FloatingLabels";
import ChecksRadios from "../pages/forms/ChecksRadios";
import FormRange from "../pages/forms/FormRange";
import FormValidation from "../pages/forms/FormValidation";
import FormLayout from "../pages/forms/FormLayout";
import QuillPreview from "../pages/forms/editors/QuillPreview";
import TinymcePreview from "../pages/forms/editors/TinymcePreview";

// other pages
import DataTablePreview from "../pages/DataTablePreview";
import ChartsPreview from "../pages/ChartsPreview";
import Sweetalert from "../pages/Sweetalert";

// auths pages
import AuthRegister from "../pages/auths/AuthRegister";
import AuthLogin from "../pages/auths/AuthLogin";
import AuthReset from "../pages/auths/AuthReset";

import NotFound from "../pages/error/NotFound";
import IconsPreview from "../pages/IconsPreview";
import TopCourses from "../my-pages/cms/TopCourses";
import TopCoaches from "../my-pages/cms/TopCoaches";
import BlocksCms from "../my-pages/cms/BlocksCms";
import Testimonials from "../my-pages/cms/Testimonials";
import Booking from "../my-pages/Bookings/Booking";
import Bookingdetails from "../my-pages/Bookings/Bookingdetails";
import PlayerList from "../my-pages/players/PlayersList";
import CoachDetailsPage from "../my-pages/coach-page/CoachDetailsPage";
import CoachListPage from "../my-pages/coach-page/CoachListPage";
import CoachCreation from "../my-pages/coach-page/CoachCreation";
import CourseListPage from "../my-pages/course-page/CourseListPage";
import CourseCreation from "../my-pages/course-page/CourseCreation";
import CourseDetailPage from "../my-pages/course-page/CourseDetailPage";
import Login from "../my-pages/Auth/Login";
import AdminUser from "../my-pages/user/AdminUser";
import AdminRoles from "../my-pages/user/AdminRoles";
import Contact from "../my-pages/Contact/Contact";
import Report from "../my-pages/Report/Report";
import ForgotPassword from "../my-pages/Auth/ForgotPassword";
import ResetPassword from "../my-pages/Auth/ResetPassword";
import Register from "../my-pages/Auth/Register";
import VerificationPending from "../my-pages/Auth/VerificationPending";
import Dashboard from "../my-pages/Dashboard/Dashboard";
import TopFacility from "../my-pages/cms/TopFacility";
import DescriptionPageCms from "../my-pages/cms/DescriptionPageCms";
import EditProfile from "../my-pages/EditProfile/EditProfile";
import GoogleSignIn from "../my-pages/Auth/GoogleSignIn";
import AcademyReport from "../my-pages/AcademyReport/AcademyReport";

// Helper function to check authorization status
const checkAuthorizationStatus = () => {
  const token = sessionStorage.getItem("admintoken");
  if (!token) return { isAuthenticated: false, isAuthorized: false };

  try {
    const decodedToken = jwtDecode(token.split(" ")[1]);
    const isAuthenticated = decodedToken.exp * 1000 > Date.now();
    const isAuthorized = decodedToken?.academyId?.authStatus === "authorized";
    return { isAuthenticated, isAuthorized };
  } catch (error) {
    return { isAuthenticated: false, isAuthorized: false };
  }
};

function Router({ auth }) {
  const location = useLocation();
  const { isAuthenticated, isAuthorized } = checkAuthorizationStatus();

  return (
    <ScrollToTop>
      <Routes>
        <Route
          path="/"
          element={
            auth ? (
              isAuthorized ? (
                <Navigate to="/coach-page" />
              ) : (
                <Navigate to="/verification-pending" />
              )
            ) : (
              <Navigate to="/login" />
            )
          }
        />

        <Route
          path="/dashboard"
          element={
            auth ? (
              isAuthorized ? (
                <Dashboard />
              ) : (
                <Navigate to="/verification-pending" />
              )
            ) : (
              <Navigate to="/login" />
            )
          }
        />

        <Route
          path="/login"
          element={
            auth ? (
              isAuthorized ? (
                <Navigate to="/dashboard" />
              ) : (
                <Navigate to="/verification-pending" />
              )
            ) : (
              <Login />
            )
          }
        />

        <Route
          path="/forgot-password"
          element={
            auth ? (
              isAuthorized ? (
                <Navigate to="/dashboard" />
              ) : (
                <Navigate to="/verification-pending" />
              )
            ) : (
              <ForgotPassword />
            )
          }
        />

        <Route path="/resetPassword" element={<ResetPassword />} />

        <Route
          path="/register"
          element={
            auth ? (
              isAuthorized ? (
                <Navigate to="/dashboard" />
              ) : (
                <Navigate to="/verification-pending" />
              )
            ) : (
              <Register />
            )
          }
        />

        <Route path="/verification-pending" element={<VerificationPending />} />

        {/* Coach Routes */}
        <Route
          path="/coach-page"
          element={
            auth && isAuthorized ? (
              <CoachListPage />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />
        <Route
          path="/coach-page/creation"
          element={
            auth && isAuthorized ? (
              <CoachCreation />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />
        <Route
          path="/coach-page/details/:id"
          element={
            auth && isAuthorized ? (
              <CoachDetailsPage />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />

        {/* Course Routes*/}
        <Route
          path="/course-page"
          element={
            auth && isAuthorized ? (
              <CourseListPage />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />
        <Route
          path="/course-page/creation/:id"
          element={
            auth && isAuthorized ? (
              <CourseCreation />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />
        <Route
          path="/course-page/details/:id"
          element={
            auth && isAuthorized ? (
              <CourseDetailPage />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />
        <Route
          path="/edit-profile"
          element={
            auth && isAuthorized ? (
              <EditProfile />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />
        {/* User Management */}
        <Route
          path="/user"
          element={
            auth && isAuthorized ? (
              <AdminUser />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />
        <Route
          path="/user-role"
          element={
            auth && isAuthorized ? (
              <AdminRoles />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />

        {/* Player Routes */}
        <Route
          path="/player-list"
          element={
            auth && isAuthorized ? (
              <PlayerList />
            ) : (
              <Navigate to={auth ? "/verification-pending" : "/login"} />
            )
          }
        />

        {/* CMS Routes */}
        <Route
          path="/cms/top-courses"
          element={auth ? <TopCourses /> : <Navigate to="/login" />}
        />
        <Route
          path="/cms/top-coaches"
          element={auth ? <TopCoaches /> : <Navigate to="/login" />}
        />
        <Route
          path="/cms/blocks"
          element={auth ? <BlocksCms /> : <Navigate to="/login" />}
        />
        <Route
          path="/cms/description"
          element={auth ? <DescriptionPageCms /> : <Navigate to="/login" />}
        />
        <Route
          path="/cms/Testimonials"
          element={auth ? <Testimonials /> : <Navigate to="/login" />}
        />
        <Route
          path="/cms/top-facilities"
          element={auth ? <TopFacility /> : <Navigate to="/login" />}
        />

        {/*Booking route*/}
        <Route
          path="/Booking"
          element={auth ? <Booking /> : <Navigate to="/login" />}
        />
        <Route
          path="/Booking/details/:id"
          element={auth ? <Bookingdetails /> : <Navigate to="/login" />}
        />

        <Route
          path="blank"
          element={auth ? <Blank /> : <Navigate to="/login" />}
        />

        <Route path="ui-elements">
          <Route
            path="accordion"
            element={auth ? <Accordion /> : <Navigate to="/login" />}
          />
          <Route
            path="alert"
            element={auth ? <Alert /> : <Navigate to="/login" />}
          />
          <Route
            path="badge"
            element={auth ? <Badge /> : <Navigate to="/login" />}
          />
          <Route
            path="breadcrumb"
            element={auth ? <Breadcrumb /> : <Navigate to="/login" />}
          />
          <Route
            path="buttons"
            element={auth ? <Buttons /> : <Navigate to="/login" />}
          />
          <Route
            path="button-group"
            element={auth ? <ButtonGroup /> : <Navigate to="/login" />}
          />
          <Route
            path="cards"
            element={auth ? <Cards /> : <Navigate to="/login" />}
          />
          <Route
            path="carousel"
            element={auth ? <Carousel /> : <Navigate to="/login" />}
          />
          <Route
            path="close-button"
            element={auth ? <CloseButton /> : <Navigate to="/login" />}
          />
          <Route
            path="collapse"
            element={auth ? <Collapse /> : <Navigate to="/login" />}
          />
          <Route
            path="dropdowns"
            element={auth ? <Dropdowns /> : <Navigate to="/login" />}
          />
          <Route
            path="list-group"
            element={auth ? <ListGroup /> : <Navigate to="/login" />}
          />
          <Route
            path="modal"
            element={auth ? <Modal /> : <Navigate to="/login" />}
          />
          <Route
            path="tabs"
            element={auth ? <Tabs /> : <Navigate to="/login" />}
          />
          <Route
            path="offcanvas"
            element={auth ? <Offcanvas /> : <Navigate to="/login" />}
          />
          <Route
            path="pagination"
            element={auth ? <Pagination /> : <Navigate to="/login" />}
          />
          <Route
            path="placeholders"
            element={auth ? <Placeholders /> : <Navigate to="/login" />}
          />
          <Route
            path="popovers"
            element={auth ? <Popovers /> : <Navigate to="/login" />}
          />
          <Route
            path="progress"
            element={auth ? <Progress /> : <Navigate to="/login" />}
          />
          <Route
            path="spinners"
            element={auth ? <Spinners /> : <Navigate to="/login" />}
          />
          <Route
            path="toasts"
            element={auth ? <Toasts /> : <Navigate to="/login" />}
          />
          <Route
            path="tooltips"
            element={auth ? <Tooltips /> : <Navigate to="/login" />}
          />
        </Route>

        <Route path="utilities">
          <Route
            path="misc"
            element={auth ? <Misc /> : <Navigate to="/login" />}
          />
          <Route
            path="typography"
            element={auth ? <Typography /> : <Navigate to="/login" />}
          />
          <Route
            path="images"
            element={auth ? <Images /> : <Navigate to="/login" />}
          />
          <Route
            path="tables"
            element={auth ? <Tables /> : <Navigate to="/login" />}
          />
          <Route
            path="background"
            element={auth ? <Background /> : <Navigate to="/login" />}
          />
          <Route
            path="borders"
            element={auth ? <Borders /> : <Navigate to="/login" />}
          />
          <Route
            path="colors"
            element={auth ? <Colors /> : <Navigate to="/login" />}
          />
          <Route
            path="flex"
            element={auth ? <Flex /> : <Navigate to="/login" />}
          />
          <Route
            path="sizing"
            element={auth ? <Sizing /> : <Navigate to="/login" />}
          />
          <Route
            path="spacing"
            element={auth ? <Spacing /> : <Navigate to="/login" />}
          />
        </Route>

        <Route path="layout">
          <Route
            path="breakpoints"
            element={auth ? <Breakpoints /> : <Navigate to="/login" />}
          />
          <Route
            path="containers"
            element={auth ? <Containers /> : <Navigate to="/login" />}
          />
          <Route
            path="gutters"
            element={auth ? <Gutters /> : <Navigate to="/login" />}
          />
        </Route>

        <Route
          path="not-found"
          element={auth ? <NotFound /> : <Navigate to="/login" />}
        />
        <Route
          path="*"
          element={auth ? <NotFound /> : <Navigate to="/login" />}
        />
        <Route
          path="/contact"
          element={auth ? <Contact /> : <Navigate to="/login" />}
        />

        <Route
          path="/report"
          element={auth ? <Report /> : <Navigate to="/login" />}
        />
          <Route
          path="/academy-report"
          element={auth ? <AcademyReport /> : <Navigate to="/login" />}
        />
        <Route path="/google-signin" element={<GoogleSignIn />} />
      </Routes>
    </ScrollToTop>
  );
}

export default Router;
