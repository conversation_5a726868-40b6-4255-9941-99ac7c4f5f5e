import {
  Box,
  Button,
  Card,
  CardBody,
  FormControl,
  FormLabel,
  Heading,
  Image,
  Input,
  useToast,
  Flex,
  Select,
  FormErrorMessage,
  RadioGroup,
  Radio,
  HStack,
  Text,
  Divider,
} from "@chakra-ui/react";
import React, { useEffect, useState, useRef } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { Link, useParams } from "react-router-dom";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";
import { Country, State, City } from "country-state-city";

const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

const KYCDetailsCoach = ({ coachData }) => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImagesError, setSelectedImagesError] = useState(false);
  const [isLoadingSubmitBtn, setIsLoadingSubmitBtn] = useState(false);
  const [isDiscardDisabled, setIsDiscardDisabled] = useState(false);
  const [formChanged, setFormChanged] = useState(false);
  const initialValuesRef = useRef(null);
  const toast = useToast();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);
  const [states, setStates] = useState([]);
  const countryCode = "IN";
  const handleFormChange = () => {
    setFormChanged(true);
    setIsDiscardDisabled(false);
  };

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const handleFileChange = async (e, index) => {
    try {
      const files = Array.from(e.currentTarget.files || []);

      const remainingSlots = 2 - selectedImages.length;
      if (remainingSlots <= 0) {
        toast({
          title: "You can upload a maximum of 2 PAN images.",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        // clear input so user can reselect later
        try { e.currentTarget.value = ""; } catch (err) {}
        return;
      }

      if (files.length === 0) return;

      if (files.length > remainingSlots) {
        toast({
          title: `You selected ${files.length} files. Only ${remainingSlots} will be uploaded to keep max 2 PAN images.`,
          status: "info",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }

      const filesToUpload = files.slice(0, remainingSlots);
      const uploaded = [];

      for (const file of filesToUpload) {
        if (file && file.size > 10 * 1024 * 1024) {
          toast({
            title: "Please select a file less than 10 MB.",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          continue;
        }

        const formData = new FormData();
        formData.append("image", file);

        try {
          const response = await axios.post(
            `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            }
          );

          const url = response?.data?.url;
          if (url) {
            uploaded.push({ url });
            toast({
              title: "Image Uploaded Successfully",
              status: "success",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title:
                "Something went wrong while uploading image, please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        } catch (err) {
          console.log(err);
          // show specific error toast if available
          if (err?.response?.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        }
      }

      if (uploaded.length > 0) {
        const updated = [...selectedImages, ...uploaded].slice(0, 2);
        setSelectedImages(updated);
        formik.setFieldValue(`kycDocuments.documentImg`, updated);
        handleFormChange();
      }
      try { e.currentTarget.value = ""; } catch (err) {}
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (url, index) => {
    if (!userData?.accessScopes?.coach?.includes("delete")) {
      toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const resp = response?.data;
        formik.setFieldValue(`kycDocuments.documentImg.${index}.url`, "");
        toast({
          title: "Image removed Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } catch (error) {
        console.log(error);
        if (error?.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    }
  };

  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

  // Add separate state for Aadhaar images
  const [selectedAadhaarImages, setSelectedAadhaarImages] = useState([]);
  const [selectedAadhaarImagesError, setSelectedAadhaarImagesError] = useState(false);

  // Create separate handler for Aadhaar file changes
  const handleAadhaarFileChange = async (e, index) => {
    try {
      const files = Array.from(e.currentTarget.files || []);
      const remainingSlots = 2 - selectedAadhaarImages.length;

      if (remainingSlots <= 0) {
        toast({
          title: "You can upload a maximum of 2 Aadhaar images.",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        try { e.currentTarget.value = ""; } catch (err) {}
        return;
      }

      if (files.length === 0) return;

      if (files.length > remainingSlots) {
        toast({
          title: `You selected ${files.length} files. Only ${remainingSlots} will be uploaded to keep max 2 Aadhaar images.`,
          status: "info",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }

      const filesToUpload = files.slice(0, remainingSlots);
      const uploadedUrls = [];

      for (const file of filesToUpload) {
        setSelectedAadhaarImagesError(false);

        if (file && file.size > 10 * 1024 * 1024) {
          toast({
            title: "Please select a file less than 10 MB.",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          continue;
        }

        const formData = new FormData();
        formData.append("image", file);

        try {
          const response = await axios.post(
            `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            }
          );

          const url = response?.data?.url;
          if (url) {
            uploadedUrls.push(url);
            toast({
              title: "Image Uploaded Successfully",
              status: "success",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          }
        } catch (err) {
          console.log(err);
          if (err?.response?.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        }
      }

      if (uploadedUrls.length > 0) {
        const updated = [...selectedAadhaarImages, ...uploadedUrls].slice(0, 2);
        setSelectedAadhaarImages(updated);
        formik.setFieldValue(`aadhaarImage`, updated);
        handleFormChange();
      }
      try { e.currentTarget.value = ""; } catch (err) {}
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const formik = useFormik({
    initialValues: {
      kycDocuments: {
        // store documentName as a string
        documentName: coachData?.kycDocuments?.documentName || "",
        documentNumber: coachData?.kycDocuments?.documentNumber,
        documentImg: coachData?.kycDocuments?.documentImg,
      } || {
        documentName: "",
        documentNumber: "",
        documentImg: [],
      },
      aadhaarNumber: coachData?.aadhaarNumber || "",
      aadhaarImage: coachData?.aadhaarImage || [],
      bankDetails: coachData?.bankDetails || {
        accountNumber: "",
        accountHolderName: "",
        ifsc: "",
      },
      hasGst: coachData?.hasGst || false,
      gstNumber: coachData?.gstNumber || "",
      gstState: coachData?.gstState || "",
    },
    validationSchema: Yup.object().shape({
      kycDocuments: Yup.object().shape({
        documentNumber: Yup.string()
          .matches(panRegex, "Please enter a valid PAN number (e.g., **********)")
          .required("PAN Number is required"),
        documentImg: Yup.array().of(
          Yup.object().shape({
            url: Yup.string().url("Invalid URL"),
          })
        ).max(2, "Only up to 2 PAN images allowed"),
      }),
      bankDetails: Yup.object().shape({
       accountNumber: Yup.string()
        .min(9, "Account number must be at least 9 digits")
        .max(18, "Account number cannot exceed 18 digits")
        .matches(/^\d+$/, "Account number must contain only digits")
        .required("Account number is required"),
      ifsc: Yup.string()
        .matches(
          /^[A-Z]{4}0[A-Z0-9]{6}$/,
          "IFSC code must be in format ABCD0123456"
        )
        .required("IFSC code is required"),
        accountHolderName: Yup.string()
          .required("Account Holder Name is required")
          .min(3, "Account holder name must be atleast 3 character")
          .max(
            100,
            "Account holder name must be less than or equal to 100 characters"
          )
          .matches(
            /^[A-Za-z\s]+$/,
            "Account holder name should only contain alphabets"
          ),
      }),
      hasGst: Yup.boolean(),
      // gstNumber: Yup.string().matches(
      //   gstRegex,
      //   "Please enter a valid GST number"
      // ),
      gstNumber: Yup.string()
      .matches(gstRegex, "Please enter a valid GST number")
      .when("hasGst", {
        is: true,
        then: (schema) => schema.required("GST Number is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
    gstState: Yup.string()
      .when("hasGst", {
        is: true,
        then: (schema) => schema.required("GST State is required"),
        otherwise: (schema) => schema.notRequired(),
      })
      .max(100, "GST State must be less than or equal to 100 characters"),
      aadhaarNumber: Yup.string()
        .matches(/^\d{12}$/, "Aadhaar number must be exactly 12 digits")
        .required("Aadhaar number is required"),
      aadhaarImage: Yup.array()
        .min(2, "Please upload at least 2 Aadhaar images")
        .max(2, "Only up to 2 Aadhaar images allowed")
        .of(Yup.string().url("Invalid URL")),
    }),
    onSubmit: async (values) => {
      const filteredDocumentImg = values.kycDocuments.documentImg.filter(
        (doc) => doc.url !== ""
      );
      values.kycDocuments.documentImg = filteredDocumentImg;
      
      setIsLoadingSubmitBtn(true);
      
      if (selectedImages.length === 0) {
        setSelectedImagesError(true);
        setIsLoadingSubmitBtn(false);
        return;
      }
      
      if (selectedAadhaarImages.length < 2) {
        setSelectedAadhaarImagesError(true);
        setIsLoadingSubmitBtn(false);
        return;
      }
      
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach/${id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: JSON.stringify(values),
      };

      axios
        .request(config)
        .then((response) => {
          setIsLoadingSubmitBtn(false);
          toast({
            title: "Coach KYC Details updated",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log("Error in PATCH:", error);
          console.log("Error details:", {
            message: error.message,
            code: error.code,
            config: error.config,
            request: error.request
          });
          
          setIsLoadingSubmitBtn(false);

          // Check if it's a network error
          if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
            toast({
              title: "Network Error",
              description: "Unable to connect to server. Please check if the server is running.",
              status: "error",
              duration: 6000,
              position: "top",
              isClosable: true,
            });
            return;
          }

          const status = error?.response?.status;

          if (status === 403) {
            toast({
              title: "You don't have access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong. Please try again later",
              description: error?.message || "Unknown error",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });
  useEffect(() => {
    // Trim any pre-existing arrays to a maximum of 2 to enforce the limit
    const trimmedPan = (coachData?.kycDocuments?.documentImg || []).slice(0, 2);
    const trimmedAadhaar = (coachData?.aadhaarImage || []).slice(0, 2);
    setSelectedImages(trimmedPan);
    setSelectedAadhaarImages(trimmedAadhaar);
    // Keep formik values in sync with trimmed arrays
    try {
      formik.setFieldValue(`kycDocuments.documentImg`, trimmedPan);
      formik.setFieldValue(`aadhaarImage`, trimmedAadhaar);
      // documentName is now a string, so just set it directly
      formik.setFieldValue(`kycDocuments.documentName`, coachData?.kycDocuments?.documentName || "");
    } catch (err) {
      // formik may not be ready; ignore if so
    }
  }, [id]);

  useEffect(() => {
    if (formik.values && !initialValuesRef.current) {
      initialValuesRef.current = JSON.parse(JSON.stringify(formik.values));
    }
  }, [formik.values]);

  return (
    <>
      <form>
        {/* PAN Card Details */}
        <Card
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              PAN Card Details
            </Heading>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.kycDocuments?.documentNumber &&
                formik.errors.kycDocuments?.documentNumber
              }
            >
              <FormLabel htmlFor="kycDocuments.documentNumber">
                PAN Card Number <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter PAN card number"
                name="kycDocuments.documentNumber"
                id="kycDocuments.documentNumber"
                autoComplete="none"
                {...formik.getFieldProps("kycDocuments.documentNumber")}
                onChange={(e) => {
                  formik.handleChange(e);
                  handleFormChange();
                }}
              />
              {formik.touched.kycDocuments?.documentNumber &&
                formik.errors.kycDocuments?.documentNumber && (
                  <FormErrorMessage>
                    {formik.errors.kycDocuments?.documentNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <Box mt={3}>
              <Input
                id={`kycDocuments.documentImg.${0}.url`}
                name={`kycDocuments.documentImg.${0}.url`}
                type="file"
                accept="image/*"
                multiple
                disabled={selectedImages.length >= 2}
                onChange={(e) => handleFileChange(e, 0)}
              />
              {selectedImages.length >= 2 && (
                <Text color={"gray.600"} fontSize={"sm"} mt={1}>
                  Maximum 2 PAN images uploaded
                </Text>
              )}
              {selectedImagesError && selectedImages.length === 0 && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  Please select atleast one document image
                </Text>
              )}
              {formik?.values?.kycDocuments?.documentImg?.length > 0 && (
                <Box display="flex" flexWrap="wrap" mt={3}>
                  {selectedImages.map(
                    (preview, index) =>
                      preview?.url && (
                        <Box key={index} m={1} textAlign={"center"}>
                          <a
                            href={`${preview?.url}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Image
                              src={preview?.url}
                              alt={`Preview ${index}`}
                              height={{ base: "80px", md: "11vh" }}
                              width={{ base: "120px", md: "7vw" }}
                              objectFit="cover"
                              borderRadius="md"
                            />
                          </a>
                          {userData?.accessScopes?.coach?.includes("delete") && (
                            <Button
                              colorScheme="red"
                              size="sm"
                              mt={1}
                              onClick={() => {
                                deleteImageFiles(
                                  formik?.values?.kycDocuments?.documentImg[index]?.url,
                                  index
                                );
                                const updated = selectedImages.filter((_, i) => i !== index);
                                setSelectedImages(updated);
                                formik.setFieldValue(`kycDocuments.documentImg`, updated);
                                if (!updated || updated.length === 0) {
                                  formik.setFieldValue(`kycDocuments.documentName`, "");
                                }
                                try {
                                  const panInput = document.getElementById(`kycDocuments.documentImg.${0}.url`);
                                  if (panInput) panInput.value = "";
                                } catch (err) {
                                  console.error(err);
                                }
                                handleFormChange();
                              }}
                            >
                              Remove
                            </Button>
                          )}
                        </Box>
                      )
                  )}
                </Box>
              )}
            </Box>
          </CardBody>
        </Card>

        {/* Aadhaar Card Details */}
        <Card
          mt={4}
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              Aadhaar Card Details
            </Heading>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.aadhaarNumber &&
                formik.errors.aadhaarNumber
              }
            >
              <FormLabel htmlFor="aadhaarNumber">
                Aadhar Number <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter Aadhar Card Number"
                name="aadhaarNumber"
                id="aadhaarNumber"
                autoComplete="none"
                {...formik.getFieldProps("aadhaarNumber")}
                onChange={(e) => {
                  formik.handleChange(e);
                  handleFormChange();
                }}
              />
              {formik.touched.aadhaarNumber &&
                formik.errors.aadhaarNumber && (
                  <FormErrorMessage>
                    {formik.errors.aadhaarNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <Box mt={3}>
              <Input
                id={`aadhaarImage.${0}.url`}
                name={`aadhaarImage.${0}.url`}
                type="file"
                accept="image/*"
                multiple
                disabled={selectedAadhaarImages.length >= 2}
                onChange={(e) => handleAadhaarFileChange(e, 0)}
              />
              {selectedAadhaarImagesError && selectedAadhaarImages.length < 2 && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  Please select at least 2 Aadhaar images
                </Text>
              )}
              {selectedAadhaarImages.length >= 2 && (
                <Text color={"gray.600"} fontSize={"sm"} mt={1}>
                  Maximum 2 Aadhaar images uploaded
                </Text>
              )}
              {formik.errors.aadhaarImage && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  {formik.errors.aadhaarImage}
                </Text>
              )}
              {selectedAadhaarImages.length > 0 && (
                <Box display="flex" flexWrap="wrap" mt={3}>
                  {selectedAadhaarImages.map(
                    (imageUrl, index) =>
                      imageUrl && (
                        <Box key={index} m={1} textAlign={"center"}>
                          <a
                            href={imageUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Image
                              src={imageUrl}
                              alt={`Aadhaar Preview ${index}`}
                              height={{ base: "80px", md: "11vh" }}
                              width={{ base: "120px", md: "7vw" }}
                              objectFit="cover"
                              borderRadius="md"
                            />
                          </a>
                          {userData?.accessScopes?.coach?.includes("delete") && (
                            <Button
                              colorScheme="red"
                              size="sm"
                              mt={1}
                              onClick={() => {
                                // Remove by index to avoid accidentally removing duplicates
                                const updatedImages = selectedAadhaarImages.filter((_, i) => i !== index);
                                setSelectedAadhaarImages(updatedImages);
                                formik.setFieldValue(`aadhaarImage`, updatedImages);
                                // If no Aadhaar images remain, do NOT clear Aadhaar number here (keep the number intact)
                                // Also clear the Aadhaar file input so browser preview is removed
                                try {
                                  const aadhaarInput = document.getElementById(`aadhaarImage.${0}.url`);
                                  if (aadhaarInput) aadhaarInput.value = "";
                                } catch (err) {
                                  // ignore
                                }
                                handleFormChange();
                              }}
                            >
                              Remove
                            </Button>
                          )}
                        </Box>
                      )
                  )}
                </Box>
              )}
            </Box>
          </CardBody>
        </Card>
        {/* Account Details */}
        <Card
          mt={4}
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              Account Details
            </Heading>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.accountHolderName &&
                formik.errors.bankDetails?.accountHolderName
              }
            >
              <FormLabel htmlFor="bankDetails.accountHolderName">
                Account Holder Name <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter account holder name"
                name="bankDetails.accountHolderName"
                id="bankDetails.accountHolderName"
                autoComplete="none"
                value={formik.values.bankDetails?.accountHolderName || ""}
                onChange={(e) => {
                  const value = e.target.value.replace(/[^A-Za-z\s]/g, '');
                  formik.setFieldValue("bankDetails.accountHolderName", value);
                  handleFormChange();
                }}
                onBlur={formik.handleBlur}
              />
              {formik.touched.bankDetails?.accountHolderName &&
                formik.errors.bankDetails?.accountHolderName && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.accountHolderName}
                  </FormErrorMessage>
                )}
            </FormControl>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.accountNumber &&
                formik.errors.bankDetails?.accountNumber
              }
            >
              <FormLabel>
                Account No. <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                pattern="\d*"
                placeholder="Enter account number"
                name="bankDetails.accountNumber"
                id="bankDetails.accountNumber"
                {...formik.getFieldProps("bankDetails.accountNumber")}
                onChange={(e) => {
                  formik.handleChange(e);
                  handleFormChange();
                }}
                autoComplete="none"
              />
              {formik.touched.bankDetails?.accountNumber &&
                formik.errors.bankDetails?.accountNumber && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.accountNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.ifsc &&
                formik.errors.bankDetails?.ifsc
              }
            >
              <FormLabel>
                IFSC Code <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                name="bankDetails.ifsc"
                id="bankDetails.ifsc"
                autoComplete="none"
                placeholder="Enter IFSC code"
                {...formik.getFieldProps("bankDetails.ifsc")}
                onChange={(e) => {
                  formik.handleChange(e);
                  handleFormChange();
                }}
              />
              {formik.touched.bankDetails?.ifsc &&
                formik.errors.bankDetails?.ifsc && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.ifsc}
                  </FormErrorMessage>
                )}
            </FormControl>
          </CardBody>
        </Card>

        {/*GST Details*/}

        <Card
          mt={4}
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              GST Details
            </Heading>

            <FormControl
              isInvalid={formik.touched.hasGst && formik.errors.hasGst} // Validation handling
            >
              <FormLabel htmlFor="hasGst">Has GST?</FormLabel>
              <RadioGroup
                id="hasGst"
                name="hasGst"
                onChange={(value) => {
                  formik.setFieldValue("hasGst", value == "yes" ? true : false);
                  handleFormChange();
                }}
                value={formik.values?.hasGst ? "yes" : "no"}
              >
                <HStack spacing="24px">
                  <Radio value="yes">Yes</Radio>
                  <Radio value="no">No</Radio>
                </HStack>
              </RadioGroup>
              <FormErrorMessage>{formik.errors.hasGst}</FormErrorMessage>
            </FormControl>

            {formik.values.hasGst && (
              <FormControl
                mb={2}
                isInvalid={formik.touched.gstNumber && formik.errors.gstNumber}
              >
                <FormLabel>GST No.</FormLabel>
                <Input
                  type="text"
                  pattern="\d*"
                  placeholder="Enter GST number"
                  name="gstNumber"
                  id="gstNumber"
                  {...formik.getFieldProps("gstNumber")}
                  onChange={(e) => {
                    formik.handleChange(e);
                    handleFormChange();
                  }}
                  autoComplete="none"
                />
                {formik.touched.gstNumber && formik.errors.gstNumber && (
                  <FormErrorMessage>{formik.errors.gstNumber}</FormErrorMessage>
                )}
              </FormControl>
            )}
            {formik.values.hasGst && (
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.gstState && formik.errors.gstState}
              >
                <FormLabel htmlFor="gstState">GST State</FormLabel>

                {formik.values.hasGst && (
                  // Display the state name based on gstState (
                  // Render Select input if gstState is not available
                  <Select
                    placeholder="Select GST State"
                    name="gstState"
                    id="gstState"
                    autoComplete="address-level1"
                    {...formik.getFieldProps("gstState")}
                    onChange={(e) => {
                      formik.handleChange(e);
                      handleFormChange();
                    }}
                  >
                    <option value="">Select State</option>
                    {states.map((state) => (
                      <option key={state.isoCode} value={state.isoCode}>
                        {state.name}
                      </option>
                    ))}
                  </Select>
                )}

                <FormErrorMessage>{formik.errors.gstState}</FormErrorMessage>
              </FormControl>
            )}
          </CardBody>
        </Card>

        {userData?.accessScopes?.coach?.includes("write") && (
          <Flex justifyContent={"space-between"} alignItems={"center"} mt={4}>
            <Button
              colorScheme="red"
              flexBasis={"49%"}
              isDisabled={isDiscardDisabled || !formChanged}
              onClick={() => {
                formik.resetForm();
                setIsDiscardDisabled(true);
                setFormChanged(false);
                toast({
                  title: "All Changes has been discarded",
                  status: "success",
                  duration: 4000,
                  position: "top",
                  isClosable: true,
                });
              }}
            >
              Discard
            </Button>
            <Button
              colorScheme="green"
              flexBasis={"49%"}
              type="submit"
              onClick={formik.handleSubmit}
              isLoading={isLoadingSubmitBtn}
            >
              Update
            </Button>
          </Flex>
        )}
      </form>
    </>
  );
};

export default KYCDetailsCoach;
