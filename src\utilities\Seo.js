import React from "react";
import { Helmet } from "react-helmet-async";

const getRobotsContent = (environment) => {
  if (environment === "production") return "index, follow";
  return "noindex, nofollow";
};

export function Seo({
  title,
  description,
  canonical,
  image,
  url,
  robots,
  twitterCard = "summary_large_image",
  jsonLd,
  siteName = "KhelSport",
  locale = "en_US",
}) {
  const env =
    process.env.REACT_APP_ENVIRONMENT 
  const robotsContent = robots || getRobotsContent(env);
  const resolvedCanonical = canonical || url || window.location?.href;

  const metaTags = [
    { name: "robots", content: robotsContent },
    description ? { name: "description", content: description } : null,
    title ? { property: "og:title", content: title } : null,
    description ? { property: "og:description", content: description } : null,
    image ? { property: "og:image", content: image } : null,
    url ? { property: "og:url", content: url } : null,
    { property: "og:site_name", content: siteName },
    { property: "og:locale", content: locale },
    { name: "twitter:card", content: twitterCard },
    title ? { name: "twitter:title", content: title } : null,
    description ? { name: "twitter:description", content: description } : null,
    image ? { name: "twitter:image", content: image } : null,
  ].filter(Boolean);

  return (
    <Helmet>
      {title && <title>{title}</title>}
      {resolvedCanonical && (
        <link rel="canonical" href={resolvedCanonical} />
      )}
      {metaTags.map((props, idx) => (
        <meta key={idx} {...props} />
      ))}
      {jsonLd && (
        <script type="application/ld+json">
          {JSON.stringify(jsonLd)}
        </script>
      )}
    </Helmet>
  );
}

// JSON-LD helpers
export const schema = {
  organization: ({ name, url, logo, sameAs = [] }) => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    name,
    url,
    logo,
    sameAs,
  }),
  productOrPerson: ({
    type = "Product",
    name,
    description,
    image,
    sku,
    brand,
    offers,
    personProps,
  }) => {
    if (type === "Person") {
      return {
        "@context": "https://schema.org",
        "@type": "Person",
        name,
        description,
        image,
        ...personProps,
      };
    }
    return {
      "@context": "https://schema.org",
      "@type": "Product",
      name,
      description,
      image,
      sku,
      brand: brand ? { "@type": "Brand", name: brand } : undefined,
      offers,
    };
  },
  course: ({ name, description, url, provider, image }) => ({
    "@context": "https://schema.org",
    "@type": "Course",
    name,
    description,
    provider: provider
      ? { "@type": "Organization", name: provider.name, sameAs: provider.sameAs }
      : undefined,
    url,
    image,
  }),
  localBusiness: ({
    name,
    url,
    telephone,
    address,
    image,
    openingHours,
    geo,
    priceRange,
  }) => ({
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name,
    url,
    image,
    telephone,
    address,
    openingHours,
    geo,
    priceRange,
  }),
};

export default Seo;

