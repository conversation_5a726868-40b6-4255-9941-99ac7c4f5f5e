import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  <PERSON><PERSON>,
  Card,
  <PERSON><PERSON>ody,
  <PERSON>ack,
  Toolt<PERSON>,
} from "@chakra-ui/react";
import { MdDelete, MdEdit } from "react-icons/md";

export default function AdminUserMobilePage({
  user,
  isLoading,
  userData,
  handleEditUserButtonClicked,
  openConfirmModal,
}) {
  return (
    <Box px={{base: 0, md: 1}} py={2}>
      {/* Card List */}
      <Stack spacing={4}>
        {user && user.length > 0 ? (
          user.map((item, i) => (
              <Card key={item._id} border="1px solid #CBD5E0">
                <CardBody>
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>
                      S.No:
                    </Text>
                    <Text fontSize="sm" fontWeight="normal">
                      {i + 1}
                    </Text>
                  </Flex>
                  
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>
                      Name:
                    </Text>
                    <Text fontSize="sm" fontWeight="normal">
                      {item.name}
                    </Text>
                  </Flex>
                  
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>
                      Email:
                    </Text>
                    <Text fontSize="sm" fontWeight="normal" isTruncated maxW="150px">
                      {item.email}
                    </Text>
                  </Flex>
                  
                  <Flex justify="space-between" align="flex-start" mb="10px">
                    <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>
                      Roles:
                    </Text>
                    <Box textAlign="right" maxW="150px">
                      {item.academyUserGroups?.map((group, i) => (
                        <Box key={i} mb={1}>
                          <Badge 
                            colorScheme={i % 2 === 0 ? "purple" : "green"}
                            fontSize="xs"
                          >
                            {group.name}
                          </Badge>
                        </Box>
                      ))}
                      {(!item.academyUserGroups || item.academyUserGroups.length === 0) && (
                        <Text fontSize="sm" color="gray.500">
                          No roles assigned
                        </Text>
                      )}
                    </Box>
                  </Flex>

                  {/* Action Buttons */}
                  {process.env.REACT_APP_SUPER_ADMIN_USER_ID !== item._id &&
                   userData._id !== item._id ? (
                    <Flex justify="flex-end" mt={3} gap={2}>
                      {userData?.accessScopes?.user?.includes("write") && (
                        <Tooltip
                          label={
                            item.isDefaultAdmin
                              ? "Cannot edit default admin user"
                              : "Edit User"
                          }
                        >
                          <Button
                            size="sm"
                            colorScheme="blue"
                            variant="outline"
                            leftIcon={<MdEdit />}
                            onClick={() => !item.isDefaultAdmin && handleEditUserButtonClicked(item)}
                            isDisabled={item.isDefaultAdmin}
                            opacity={item.isDefaultAdmin ? 0.5 : 1}
                            cursor={item.isDefaultAdmin ? "not-allowed" : "pointer"}
                          >
                            Edit
                          </Button>
                        </Tooltip>
                      )}
                      {userData?.accessScopes?.user?.includes("delete") && (
                        <Tooltip
                          label={
                            item.isDefaultAdmin
                              ? "Cannot delete default admin user"
                              : "Delete User"
                          }
                        >
                          <Button
                            size="sm"
                            colorScheme="red"
                            variant="outline"
                            leftIcon={<MdDelete />}
                            onClick={() => !item.isDefaultAdmin && openConfirmModal(item._id)}
                            isDisabled={item.isDefaultAdmin}
                            opacity={item.isDefaultAdmin ? 0.5 : 1}
                            cursor={item.isDefaultAdmin ? "not-allowed" : "pointer"}
                          >
                            Delete
                          </Button>
                        </Tooltip>
                      )}
                    </Flex>
                  ) : userData._id === item._id ? (
                    <Flex justify="flex-end" mt={3}>
                      <Badge variant="solid" colorScheme="teal">
                        Logged In
                      </Badge>
                    </Flex>
                  ) : item.isDefaultAdmin ? (
                    <Flex justify="flex-end" mt={3}>
                      <Badge variant="solid" colorScheme="blue">
                        Default Admin
                      </Badge>
                    </Flex>
                  ) : null}
                </CardBody>
              </Card>
            ))
        ) : (
          <Text color="gray.500" textAlign="center" py={6}>
            No user details to show
          </Text>
        )}
      </Stack>
    </Box>
  );
}
