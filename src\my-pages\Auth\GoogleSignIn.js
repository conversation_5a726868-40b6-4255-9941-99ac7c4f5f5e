import React from "react";
import {
  <PERSON>,
  Button,
  Flex,
  Heading,
  Image,
  Text,
  VStack,
} from "@chakra-ui/react";
import Layout from "../../layout/default";

const GoogleSignIn = () => {

  return (
    <Layout title="Google Calendar Integration">
      <Flex minH="70vh" align="center" justify="center">
        <Box
          maxW="md"
          w="full"
          bg="white"
          boxShadow="lg"
          rounded="lg"
          p={8}
          textAlign="center"
        >
          <VStack spacing={6}>
            <Image
              src="https://articles-images.sftcdn.net/wp-content/uploads/sites/3/2018/06/google-calendar-1024x576.jpg"
              alt="Google Calendar"
              h="20"
              w="auto"
              mx="auto"
            />
            
            <Heading
              as="h2"
              size="lg"
              fontWeight="bold"
              color="gray.900"
              lineHeight="tight"
            >
              Please connect coach to google calendar
            </Heading>
          </VStack>
        </Box>
      </Flex>
    </Layout>
  );
};

export default GoogleSignIn;
