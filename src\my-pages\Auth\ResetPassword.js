import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  Flex,
  Button,
  Stack,
  FormControl,
  Input,
  InputGroup,
  InputRightElement,
  InputLeftElement,
  useToast,
  FormErrorMessage,
  Box,
  Image,
  chakra,
} from "@chakra-ui/react";
import { FaLock } from "react-icons/fa";
import { useFormik } from "formik";
import * as Yup from "yup";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";

const CFaLock = chakra(FaLock);

const ResetPassword = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const toast = useToast();

  // Redirect if no token is provided
  useEffect(() => {
    if (!token) {
      toast({
        title: "Invalid Reset Link",
        description: "The reset password link is invalid or has expired.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      navigate("/login");
    }
  }, [token, navigate, toast]);

  const formik = useFormik({
    initialValues: {
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      newPassword: Yup.string()
        .min(6, "Password must be at least 6 characters")
        .required("New password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("newPassword"), null], "Passwords must match")
        .required("Please confirm your password"),
    }),
    onSubmit: async (values) => {
      if (isLoading) return; // Prevent double submission

      setIsLoading(true);

      try {
        const response = await fetch(
          `${process.env.REACT_APP_BASE_URL}/api/academy/resetPassword`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              token: token,
              newPassword: values.newPassword,
            }),
          }
        );

        const data = await response.json();

        if (response.ok) {
          setSuccess(true);
          toast({
            title: "Password Reset Successful",
            description: "Your password has been reset successfully. You can now login with your new password.",
            status: "success",
            duration: 5000,
            isClosable: true,
          });
          
          // Redirect to login after 3 seconds
          setTimeout(() => {
            navigate("/login");
          }, 3000);
        } else {
          toast({
            title: "Reset Failed",
            description: data.message || "Failed to reset password. Please try again.",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("Reset password error:", error);
        toast({
          title: "Network Error",
          description: "Please check your connection and try again.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    },
  });

  if (success) {
    return (
      <Flex
        flexDirection="column"
        width="100wh"
        height="100vh"
        backgroundColor="gray.200"
        justifyContent="center"
        alignItems="center"
      >
        <Stack
          flexDir="column"
          mb="2"
          justifyContent="center"
          alignItems="center"
        >
          <Image src={LogoImage} alt="logo" w={"200px"} mb={4} />
          <Box minW={{ base: "90%", md: "468px" }}>
            <Stack
              spacing={4}
              p="1rem"
              backgroundColor="whiteAlpha.900"
              boxShadow="md"
              textAlign="center"
            >
              <Box color="green.500" fontSize="lg" fontWeight="bold">
                Password Reset Successful!
              </Box>
              <Box color="gray.600" fontSize="sm">
                Your password has been reset successfully. You will be redirected to the login page shortly.
              </Box>
              <Button
                borderRadius={0}
                colorScheme="teal"
                width="full"
                onClick={() => navigate("/login")}
              >
                Go to Login
              </Button>
            </Stack>
          </Box>
        </Stack>
      </Flex>
    );
  }

  return (
    <Flex
      flexDirection="column"
      width="100wh"
      height="100vh"
      backgroundColor="gray.200"
      justifyContent="center"
      alignItems="center"
    >
      <Stack
        flexDir="column"
        mb="2"
        justifyContent="center"
        alignItems="center"
      >
        <Image src={LogoImage} alt="logo" w={"200px"} mb={4} />
        <form onSubmit={formik.handleSubmit}>
          <Box minW={{ base: "90%", md: "468px" }}>
            <Stack
              spacing={4}
              p="1rem"
              backgroundColor="whiteAlpha.900"
              boxShadow="md"
            >
              <FormControl
                isInvalid={formik.touched.newPassword && formik.errors.newPassword}
              >
                <InputGroup>
                  <InputLeftElement
                    pointerEvents="none"
                    color="gray.300"
                    children={<CFaLock color="gray.300" />}
                  />
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter new password"
                    {...formik.getFieldProps("newPassword")}
                  />
                  <InputRightElement width="4.5rem">
                    <Button h="1.75rem" size="sm" onClick={() => setShowPassword(!showPassword)}>
                      {showPassword ? "Hide" : "Show"}
                    </Button>
                  </InputRightElement>
                </InputGroup>
                <FormErrorMessage>{formik.errors.newPassword}</FormErrorMessage>
              </FormControl>

              <FormControl
                isInvalid={formik.touched.confirmPassword && formik.errors.confirmPassword}
              >
                <InputGroup>
                  <InputLeftElement
                    pointerEvents="none"
                    color="gray.300"
                    children={<CFaLock color="gray.300" />}
                  />
                  <Input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm new password"
                    {...formik.getFieldProps("confirmPassword")}
                  />
                  <InputRightElement width="4.5rem">
                    <Button h="1.75rem" size="sm" onClick={() => setShowConfirmPassword(!showConfirmPassword)}>
                      {showConfirmPassword ? "Hide" : "Show"}
                    </Button>
                  </InputRightElement>
                </InputGroup>
                <FormErrorMessage>{formik.errors.confirmPassword}</FormErrorMessage>
              </FormControl>

              <Button
                borderRadius={0}
                type="submit"
                variant="solid"
                colorScheme="teal"
                width="full"
                isLoading={isLoading}
                loadingText="Resetting Password..."
              >
                Reset Password
              </Button>

              <Box textAlign="center" fontSize="sm" color="gray.600">
                Remember your password?{" "}
                <chakra.span
                  color="teal.500"
                  cursor="pointer"
                  onClick={() => navigate("/login")}
                >
                  Back to Login
                </chakra.span>
              </Box>
            </Stack>
          </Box>
        </form>
      </Stack>
    </Flex>
  );
};

export default ResetPassword;
