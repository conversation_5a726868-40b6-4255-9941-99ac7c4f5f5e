import React, { Fragment, useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Box,
  Flex,
  Button,
  Text,
  Input,
  Modal,
  FormControl,
  FormLabel,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Td,
  TableContainer,
  useDisclosure,
  Checkbox,
  InputGroup,
  InputRightElement,
  useToast,
  Badge,
  Spinner,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  useBreakpointValue,
} from "@chakra-ui/react";
import axios from "axios";
import { setToast } from "../../functions/Toastfunction";
import ConfirmationModal from "../../functions/Modals/ConfimationModals";
import { Link, useNavigate } from "react-router-dom";
import { MdDelete, MdEdit } from "react-icons/md";
import { useSelector } from "react-redux";
import AdminUserMobilePage from "./AdminUserMobilePage";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const AdminUsers = () => {
  const [userid, setUserid] = useState(null);
  const [confirmisOpen, setConfirmisOpen] = useState(false);
  const openConfirmModal = (id) => {
    setConfirmisOpen(true);
    setUserid(id);
  };
  const closeConfirmModal = () => setConfirmisOpen(false);
  const [showupdatepassword, setShowUpdatepassword] = useState(false);
  const [updatebutton, setUpdateButton] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const navigate = useNavigate();
  const [pass, setPass] = useState(false);
  const handlepassClick = () => setPass(!pass);
  const [confirmpass, setConfirmpass] = useState(false);
  const handleconfirmpassClick = () => setConfirmpass(!confirmpass);
  const [roles, setRoles] = useState([]);
  const [user, setUser] = useState(null);
  const [render, setRender] = useState(false);
  const [toggle, setToggle] = useState(false);
  const [query, setQuery] = useState("");
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [showSelectedRoles, setShowSelectedRoles] = useState([]);
  const [userDetail, setUserDetail] = useState({
    name: "",
    email: "",
    password: "",
    confirm_password: "",
  });
  const userData = useSelector((state) => state.user);

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });
  const addRolesHandler = (e) => {
    e.stopPropagation();
    setToggle(true);
  };

  function handlesaveSelectedRoles() {
    setToggle(false);
    setQuery("");
    setShowSelectedRoles(selectedRoles);
  }

  function validateEmail(email) {
    var re = /\S+@\S+\.\S+/;
    return re.test(email);
  }

  const [updateloader, setUpdateLoader] = useState(false);
  function handleUpdateUserHitApi() {
    if (showupdatepassword) {
      if (userDetail.password === "" || userDetail.confirm_password === "") {
        setToast(toast, "Please Enter Password", "", "error");
        return;
      }
      if (userDetail.password !== userDetail.confirm_password) {
        setToast(toast, "Password Does not Match", "", "error");
        return;
      }
    }

    if (showupdatepassword && userDetail.confirm_password === undefined) {
      setToast(toast, "Please Enter Password", "", "error");
      return;
    }
    if (!validateEmail(userDetail.email)) {
      setToast(toast, "Please Enter Valid Email", "", "error");
      return;
    }

    console.log(selectedRoles.length, "selected roles here");

    if (selectedRoles.length === 0) {
      setToast(toast, "Please select at least one role to continue", "", "error");
      return;
    }

    setUpdateLoader(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };

    // Prepare clean data object
    const updateData = {
      name: userDetail.name,
      email: userDetail.email,
      academyUserGroups: selectedRoles,
    };

    // Only include password if updating password
    if (showupdatepassword) {
      updateData.password = userDetail.password;
    }

    axios({
      method: "PATCH",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-users/${userid}`,
      data: updateData,
      headers,
    })
      .then((r) => {
        setToast(toast, `User Updated Successfully `, "", "success");
        onClose();
        setRender(!render);
        setUpdateLoader(false);
      })
      .catch((err) => {
        if (err.response?.data?.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setUpdateLoader(false);
        if (err.response?.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(
            toast,
            `${err.response?.data?.err || "An error occurred"}`,
            "",
            "error"
          );
        }
      });
  }

  function handleEditUserButtonClicked(user) {
    setUserid(user._id);
    const merged = Object.assign(userDetail, user);

    setUserDetail(merged);
    setSelectedRoles(user.academyUserGroups?.map((group) => group._id) || []);
    setShowSelectedRoles(
      user.academyUserGroups?.map((group) => group._id) || []
    );
    setUpdateButton(true);
    onOpen();
  }

  function handleInputChange(e) {
    const { name, value } = e.target;

    // Check if the first character is a space
    if (value.charAt(0) === " ") {
      // Remove the first character (space)
      e.target.value = value.slice(1);
      return;
    }
    if (name === "confirm_password") {
      if (e.nativeEvent?.inputType === "deleteContentBackward") {
        setUserDetail({
          ...userDetail,
          [name]: value,
        });
        return;
      }
      if (userDetail.password === "") {
        setToast(toast, "Enter your password First", "", "error");
        return;
      }
    }

    setUserDetail({
      ...userDetail,
      [name]: value,
    });
  }

  const [createloader, setCreateLoader] = useState(false);
  const handleCreateUserHitApi = () => {
    if (createloader) {
      return;
    }
    if (userDetail.password !== userDetail.confirm_password) {
      setToast(toast, "Password Does not Match", "", "error");
      return;
    }

    if (!validateEmail(userDetail.email)) {
      setToast(toast, "Please Enter Valid Email", "", "error");
      return;
    }

    if (selectedRoles.length === 0) {
      setToast(toast, "Please select at least one role to continue", "", "error");
      return;
    }

    setCreateLoader(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-users`,
      data: {
        name: userDetail.name,
        email: userDetail.email,
        password: userDetail.password,
        academyUserGroups: selectedRoles,
      },
      headers,
    })
      .then((r) => {
        setCreateLoader(false);
        setToast(toast, `User Successfully Created`, "", "success");
        onClose();
        setRender(!render);
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setCreateLoader(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          const errorData = err.response.data;

          // Check if errorData.errors is an array
          if (Array.isArray(errorData.errors)) {
            const combinedMessages = errorData.errors
              .map((e) => `${e.message}`)
              .join(", "); // Or use "\n" if you want each on a new line

            setToast(toast, combinedMessages, "", "error");
          } else {
            // fallback for non-array error message
            setToast(
              toast,
              `${errorData.message || "Something went wrong"}`,
              "",
              "error"
            );
          }
        }
      });
  };

  let [userLoading, setUserLoading] = useState(false);
  function getAllUsersFromBackend() {
    if (userLoading) {
      return;
    }
    setUserLoading(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "GET",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-users`,
      headers,
    })
      .then((r) => {
        const usersData = r.data?.data?.users || [];
        setUser(usersData);
        setUserLoading(false);
      })
      .catch((err) => {
        if (err.response?.data?.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        if (err.response?.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(
            toast,
            `${err.response?.data?.err || "An error occurred"}`,
            "",
            "error"
          );
        }
        setUserLoading(false);
      });
  }
  const [deleteloading, setDeleteLoading] = useState(false);
  function handleDeleteUser() {
    if (deleteloading) {
      return;
    }
    setDeleteLoading(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "DELETE",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-users/${userid}`,
      headers,
    })
      .then((r) => {
        setDeleteLoading(false);
        setToast(toast, `${r.data.message}`, "", "success");
        setRender(!render);
        closeConfirmModal();
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setDeleteLoading(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          const errorData = err.response.data;

          // Check if errorData.errors is an array
          if (Array.isArray(errorData.errors)) {
            const combinedMessages = errorData.errors
              .map((e) => `${e.message}`)
              .join(", "); // Or use "\n" if you want each on a new line

            setToast(toast, combinedMessages, "", "error");
          } else {
            // fallback for non-array error message
            setToast(
              toast,
              `${errorData.message || "Something went wrong"}`,
              "",
              "error"
            );
          }
        }
      });
  }

  function getUserRolesfromBackend() {
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "GET",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-user-groups`,
      headers,
    })
      .then((r) => {
        const rolesData = r.data?.data?.userGroups || [];
        setRoles(rolesData);
      })
      .catch((err) => {
        if (err.response?.data?.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        if (err.response?.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(
            toast,
            `${err.response?.data?.err || "An error occurred"}`,
            "",
            "error"
          );
        }
      });
  }

  useEffect(() => {
    getAllUsersFromBackend();
    getUserRolesfromBackend();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [render]);

  return (
    <Box w="100%" maxW="none">
      <Layout title="User | Administration">
        <Flex
          justifyContent={"space-between"}
          alignItems={"flex-start"}
          mb={3}
          flexDirection={{ base: "column", md: "row" }}
          gap={{ base: 6, md: 0 }}
        >
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            ></Button>
            <Breadcrumb fontWeight="medium" fontSize="18px">
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">User</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userLoading ||
          !userData?.accessScopes?.user?.includes("write") ? null : (
            <Flex justifyContent="space-between">
              <Flex gap="10px">
                <Button
                  variant={"outline"}
                  colorScheme="teal"
                  size={"sm"}
                  py={5}
                  px={4}
                  onClick={() => {
                    setUpdateButton(false);
                    onOpen();
                    setUserDetail({
                      name: "",
                      email: "",
                      password: "",
                      confirm_password: "",
                    });
                    setSelectedRoles([]);
                    setShowSelectedRoles([]);
                  }}
                >
                  Create Users
                </Button>
              </Flex>
            </Flex>
          )}
        </Flex>
        {userLoading ? (
          <Flex justifyContent={"center"} alignItems={"center"} mt={16}>
            <Spinner size={"lg"} />
          </Flex>
        ) : (
          <Box width="100%" maxW="none" mx={0} px={0}>
            {/* Mobile View */}
            {isMobile ? (
              <AdminUserMobilePage
                user={user}
                isLoading={userLoading}
                userData={userData}
                handleEditUserButtonClicked={handleEditUserButtonClicked}
                openConfirmModal={openConfirmModal}
              />
            ) : (
              /* Desktop View */
              <Box
                w="100%"
                maxW="none"
                borderRadius="lg"
                overflow="hidden"
                boxShadow="sm"
                bg="white"
                mx={0}
                px={0}
              >
                <TableContainer w="100%" maxW="none" overflowX="auto">
                  <Table variant="simple" size="sm" w="100%">
                    <Thead bg={"#E2DFDF"}>
                      <Tr>
                        <Th>S.No</Th>
                        <Th>Name</Th>
                        <Th>Email</Th>
                        <Th>Role</Th>
                        <Th>Action</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {user?.map((item, i) => (
                        <Tr key={i}>
                          <Td fontSize={"14px"}>{i + 1}.</Td>
                          <Td fontSize={"14px"}>{item.name}</Td>
                          <Td fontSize={"14px"}>{item.email}</Td>
                          <Td fontSize={"14px"}>
                            {item.academyUserGroups?.map((group, i) => (
                              <Box key={i}>
                                {item.academyUserGroups.length - 1 === i ? (
                                  <Badge
                                    colorScheme={
                                      i % 2 === 0 ? "purple" : "green"
                                    }
                                  >
                                    {group.name}
                                  </Badge>
                                ) : (
                                  <Badge
                                    colorScheme={
                                      i % 2 === 1 ? "green" : "purple"
                                    }
                                    mb={1}
                                  >
                                    {group.name}
                                  </Badge>
                                )}
                              </Box>
                            ))}
                          </Td>
                          <Td fontSize={"14px"}>
                            {process.env.REACT_APP_SUPER_ADMIN_USER_ID !==
                              item._id && userData._id !== item._id ? (
                              <Flex>
                                {userData?.accessScopes?.user?.includes(
                                  "write"
                                ) && (
                                  <Flex
                                    onClick={() =>
                                      !item.isDefaultAdmin && handleEditUserButtonClicked(item)
                                    }
                                    mr={1}
                                    cursor={item.isDefaultAdmin ? "not-allowed" : "pointer"}
                                    opacity={item.isDefaultAdmin ? 0.5 : 1}
                                  >
                                    <Tooltip
                                      label={
                                        item.isDefaultAdmin
                                          ? "Cannot edit default admin user"
                                          : "Edit User"
                                      }
                                    >
                                      <Text>
                                        <MdEdit fontSize={"24px"} />
                                      </Text>
                                    </Tooltip>
                                  </Flex>
                                )}
                                {userData?.accessScopes?.user?.includes(
                                  "delete"
                                ) && (
                                  <Flex
                                    cursor={item.isDefaultAdmin ? "not-allowed" : "pointer"}
                                    opacity={item.isDefaultAdmin ? 0.5 : 1}
                                    onClick={() => !item.isDefaultAdmin && openConfirmModal(item._id)}
                                  >
                                    <Tooltip
                                      label={
                                        item.isDefaultAdmin
                                          ? "Cannot delete default admin user"
                                          : "Delete User"
                                      }
                                    >
                                      <Text>
                                        <MdDelete fontSize={"24px"} />
                                      </Text>
                                    </Tooltip>
                                  </Flex>
                                )}
                              </Flex>
                            ) : userData._id === item._id ? (
                              <Badge ariant="solid" colorScheme="teal">
                                Logged In
                              </Badge>
                            ) : item.isDefaultAdmin ? (
                              <Badge variant="solid" colorScheme="blue">
                                Default Admin
                              </Badge>
                            ) : null}
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* Modal for Create/Edit User */}
            <Modal
              closeOnOverlayClick={false}
              isOpen={isOpen}
              onClose={onClose}
              p={3}
              size={{ base: "sm", md: "5xl" }}
              isCentered
            >
              <ModalOverlay />
              <ModalContent>
                <ModalHeader>
                  {updatebutton ? "Update User" : "Create New User"}
                </ModalHeader>
                <ModalCloseButton />
                <ModalBody p={6} textAlign="left">
                  <FormControl mb="10px">
                    <FormLabel>Name</FormLabel>
                    <Input
                      onChange={handleInputChange}
                      name="name"
                      value={userDetail.name}
                      placeholder="Enter user name"
                      maxLength={50}
                    />
                  </FormControl>
                  <FormControl mb="10px">
                    <FormLabel>Email</FormLabel>
                    <Input
                      value={userDetail.email}
                      onChange={handleInputChange}
                      name="email"
                      type="email"
                      placeholder="Enter user id (email)"
                    />
                  </FormControl>

                  {/* <FormControl mb="10px">
              <FormLabel>Phone Number</FormLabel>
              <Input
                type="number"
                onChange={handleInputChange}
                name="phone"
                value={userDetail.phone}
              />
            </FormControl> */}
                  {/* ************************** FOR UPDATING ************************************** */}
                  {updatebutton ? (
                    <Box mb={3}>
                      <Checkbox
                        size="lg"
                        colorScheme="orange"
                        fontWeight="semibold"
                        mb={2}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setShowUpdatepassword(true);
                          } else {
                            setShowUpdatepassword(false);
                          }
                        }}
                      >
                        Update Password
                      </Checkbox>
                      {showupdatepassword ? (
                        <>
                          <FormControl mb="10px">
                            <FormLabel>Update Password</FormLabel>
                            <InputGroup size="md">
                              <Input
                                name="password"
                                onChange={handleInputChange}
                                pr="4.5rem"
                                type={pass ? "text" : "password"}
                                placeholder="Enter password"
                              />
                              <InputRightElement width="4.5rem">
                                <Button
                                  h="1.75rem"
                                  size="sm"
                                  onClick={handlepassClick}
                                >
                                  {pass ? "Hide" : "Show"}
                                </Button>
                              </InputRightElement>
                            </InputGroup>
                          </FormControl>
                          <FormControl mb="10px">
                            <FormLabel>Confirm Password</FormLabel>
                            <InputGroup size="md">
                              <Input
                                name="confirm_password"
                                onChange={handleInputChange}
                                pr="4.5rem"
                                type={confirmpass ? "text" : "password"}
                                placeholder="Confirm password"
                              />
                              <InputRightElement width="4.5rem">
                                <Button
                                  h="1.75rem"
                                  size="sm"
                                  onClick={handleconfirmpassClick}
                                >
                                  {confirmpass ? "Hide" : "Show"}
                                </Button>
                              </InputRightElement>
                            </InputGroup>
                          </FormControl>
                        </>
                      ) : null}
                    </Box>
                  ) : (
                    <Box>
                      <FormControl mb="10px">
                        <FormLabel>Enter Password</FormLabel>
                        <InputGroup size="md">
                          <Input
                            name="password"
                            onChange={handleInputChange}
                            pr="4.5rem"
                            type={pass ? "text" : "password"}
                            placeholder="Enter password"
                          />
                          <InputRightElement width="4.5rem">
                            <Button
                              h="1.75rem"
                              size="sm"
                              onClick={handlepassClick}
                            >
                              {pass ? "Hide" : "Show"}
                            </Button>
                          </InputRightElement>
                        </InputGroup>
                      </FormControl>
                      <FormControl mb="10px">
                        <FormLabel>Confirm Password</FormLabel>
                        <InputGroup size="md">
                          <Input
                            name="confirm_password"
                            onChange={handleInputChange}
                            pr="4.5rem"
                            type={confirmpass ? "text" : "password"}
                            placeholder="Confirm password"
                          />
                          <InputRightElement width="4.5rem">
                            <Button
                              h="1.75rem"
                              size="sm"
                              onClick={handleconfirmpassClick}
                            >
                              {confirmpass ? "Hide" : "Show"}
                            </Button>
                          </InputRightElement>
                        </InputGroup>
                      </FormControl>
                    </Box>
                  )}
                  <FormControl mb="10px">
                    <FormLabel>Roles</FormLabel>
                    <Input
                      placeholder={"Select Roles"}
                      onClick={(e) => {
                        addRolesHandler(e);
                      }}
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                    />
                    {toggle && (
                      <Box
                        textAlign="left"
                        position={"absolute"}
                        zIndex={"1"}
                        bgColor={"white"}
                        width={"100%"}
                        border="1px"
                        borderColor="gray.300"
                        rounded={"md"}
                      >
                        <Flex width={"100%"} justifyContent={"flex-end"} py={2}>
                          <Button
                            size={"xs"}
                            colorScheme="whatsapp"
                            mx={2}
                            onClick={handlesaveSelectedRoles}
                          >
                            Save Changes
                          </Button>
                        </Flex>
                        <Flex
                          maxHeight={`${window.innerHeight - 400}px`}
                          overflowY={"scroll"}
                          overflowX={"hidden"}
                        >
                          <FormControl py={2} px={4} pt={0}>
                            {roles
                              .filter((e) =>
                                e.name
                                  .toLowerCase()
                                  .includes(query.toLocaleLowerCase())
                              )
                              .map((item, i) => {
                                return (
                                  <Fragment key={i}>
                                    <Checkbox
                                      isChecked={selectedRoles.some(
                                        (val) => val === item._id
                                      )}
                                      onChange={(e) => {
                                        if (e.target.checked === true) {
                                          let arr = [...selectedRoles];
                                          arr.push(item._id);
                                          setSelectedRoles(arr);
                                        } else if (e.target.checked === false) {
                                          let arr = [...selectedRoles];
                                          let newarr = arr.filter(
                                            (val) => val !== item._id
                                          );
                                          setSelectedRoles(newarr);
                                        }
                                      }}
                                    >
                                      {item.name}
                                    </Checkbox>
                                    <br />
                                  </Fragment>
                                );
                              })}
                          </FormControl>
                        </Flex>
                      </Box>
                    )}
                  </FormControl>
                  <Flex wrap={"wrap"}>
                    {showSelectedRoles.map((item, i) => (
                      <Badge
                        key={i}
                        colorScheme={i % 2 === 0 ? "purple" : "green"}
                        mr={3}
                      >
                        {roles.find((role) => item === role._id)?.name}
                      </Badge>
                    ))}
                  </Flex>
                </ModalBody>
                <ModalFooter gap="10px">
                  {updatebutton ? (
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={"sm"}
                      py={5}
                      px={4}
                      onClick={handleUpdateUserHitApi}
                    >
                      {updateloader ? <Spinner /> : "Update"}
                    </Button>
                  ) : (
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={"sm"}
                      py={5}
                      px={4}
                      onClick={handleCreateUserHitApi}
                    >
                      {createloader ? <Spinner /> : "Save"}
                    </Button>
                  )}
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                    ml={1}
                    onClick={onClose}
                  >
                    Cancel
                  </Button>
                </ModalFooter>
              </ModalContent>
            </Modal>

            {/* Confirmation Modal for Delete */}
            <ConfirmationModal
              heading="Delete User"
              action="Are you Sure? You want to Delete this User"
              ConfirmButton="Yes Delete"
              onClickFunction={handleDeleteUser}
              isOpen={confirmisOpen}
              onClose={closeConfirmModal}
              loader={true}
              loading={deleteloading}
            />
          </Box>
        )}
      </Layout>
    </Box>
  );
};

export default AdminUsers;
