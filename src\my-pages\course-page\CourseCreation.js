import React, { useEffect, useState, useRef } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Image,
  Input,
  Select,
  Stack,
  Text,
  Tooltip,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  UnorderedList,
  ListItem,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  HStack,
  Tag,

  MenuList,
  Menu,
  MenuButton,
  CheckboxGroup,
  Checkbox,
  useMediaQuery,
} from "@chakra-ui/react";
import { FaChevronDown } from "react-icons/fa";
import { IoMdArrowRoundBack } from "react-icons/io";
import { Link, useNavigate, useParams } from "react-router-dom";
import ReactQuill from "react-quill";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./datepicker.css";
import axios from "axios";

import "react-quill/dist/quill.snow.css";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import Seo, { schema } from "../../utilities/Seo";


const proficiencyLevelOption = [
  { id: 0, name: "Beginner", value: "beginner" },
  { id: 1, name: "Intermediate", value: "intermediate" },
  { id: 2, name: "Advance", value: "advance" },
];

// Reusable styled FormErrorMessage component
const StyledFormErrorMessage = ({ children, ...props }) => (
  <FormErrorMessage
    fontSize="sm"
    mt={1}
    wordBreak="break-word"
    whiteSpace="normal"
    {...props}
  >
    {children}
  </FormErrorMessage>
);

const CourseCreation = () => {
  // Mobile responsiveness
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const [isTablet] = useMediaQuery("(max-width: 1024px)");
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImagesError, setSelectedImagesError] = useState(false);
  const [courseName, setCourseName] = useState("");
  const [courseNameError, setCourseNameError] = useState(false);
  const [isItCamp, setIsItCamp] = useState(false);
  const [campName, setCampName] = useState("");
  const [campNameError, setCampNameError] = useState(false);
  const [maximumGroupSize, setMaximumGroupSize] = useState(1);
  const [maximumGroupSizeError, setMaximumGroupSizeError] = useState(false);
  const [sessionType, setSessionType] = useState("Group");
  const [categoryType, setCategoryType] = useState("");
  const [categories, setCategories] = useState([]);
  const [courseDescription, setCourseDescription] = useState("");
  const [courseDescriptionError, setCourseDescriptionError] = useState(false);
  const [courseAmeneties, setCourseAmeneties] = useState("");
  const [btnIsLoading, setBtnIsLoading] = useState(false);
  const [facilities, setFacilities] = useState([]);
  const [selectedDays, setSelectedDays] = useState([]);
  const [isEnd, setIsEnd] = useState(false);
  const [timeFrom, setTimeFrom] = useState("");
  const [timeTo, setTimeTo] = useState("");
  const [classType, setClassType] = useState("class");
  const [startDate, setStartDate] = useState("");
  const [startDateError, setStartDateError] = useState(false);
  const [endDate, setEndDate] = useState("");
  const [price, setPrice] = useState("");
  const [priceError, setPriceError] = useState(false);
  const [fees30, setFees30] = useState("");
  const [fees60, setFees60] = useState("");
  const [fees30Error, setFees30Error] = useState(false);
  const [fees60Error, setFees60Error] = useState(false);
  const [cancellationPolicy, setCancellationPolicy] = useState("");
  const [carryThings, setCarryThings] = useState("");
  const [timeFromError, setTimeFromError] = useState(false);
  const [timeToError, setTimeToError] = useState(false);
  const [courseFacility, setCourseFacility] = useState("");
  const [categoryError, setCategoryError] = useState(false);
  const [facilityError, setFacilityError] = useState(false);
  const [selectedDaysError, setSelectedDaysError] = useState(false);
  const [coach, setCoach] = useState({});
  const [proficiencyLevel, setProficiencyLevel] = useState([]);
  const [showSlotConflict, setShowSlotConflict] = useState(false);
  const [conflictResult, setConflictResult] = useState({});
  const [academyStartTime, setAcademyStartTime] = useState(null);
  const [academyEndTime, setAcademyEndTime] = useState(null);
  const [kycChecked, setKycChecked] = useState(false);
  const [isProficiencyMenuOpen, setIsProficiencyMenuOpen] = useState(false);

  const toast = useToast();
  const navigate = useNavigate();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  // Ensure proficiency level is properly initialized
  useEffect(() => {
    // Reset proficiency level to empty array to prevent any default selections
    setProficiencyLevel([]);
  }, []);

  // Close proficiency menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isProficiencyMenuOpen) {
        const proficiencyMenu = document.getElementById('proficiency-menu');
        if (proficiencyMenu && !proficiencyMenu.contains(event.target)) {
          setIsProficiencyMenuOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isProficiencyMenuOpen]);

  // Toast management to prevent multiple toasts
  const activeToastRef = useRef(null);

  const showToast = (title, description, status = "error", duration = 2000) => {
    // Close any existing toast
    if (activeToastRef.current) {
      toast.close(activeToastRef.current);
    }

    // Show new toast and store its ID
    activeToastRef.current = toast({
      title,
      description,
      status,
      duration,
      position: "top",
      isClosable: true,
      onCloseComplete: () => {
        activeToastRef.current = null;
      },
    });
  };

  const days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  // Get available days from coach's academy availability
  const getAvailableDays = () => {
    if (coach?.academyAvailability?.days && coach.academyAvailability.days.length > 0) {
      return coach.academyAvailability.days;
    }
    return days; // fallback to all days if no restriction
  };

  const availableDays = getAvailableDays();

  // Get date range restrictions from coach's academy availability
  const getDateRestrictions = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

    if (coach?.academyAvailability?.startDate && coach?.academyAvailability?.endDate) {
      const coachStartDate = new Date(coach.academyAvailability.startDate);
      const coachEndDate = new Date(coach.academyAvailability.endDate);

      return {
        minDate: coachStartDate > today ? coachStartDate : today, // Use whichever is later
        maxDate: coachEndDate
      };
    }
    return {
      minDate: today, // Always start from today, never allow past dates
      maxDate: null // No max date restriction
    };
  };

  const dateRestrictions = getDateRestrictions();

  const getCurrentTime = () => {
    const currentDate = new Date();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    return new Date(0, 0, 0, currentHours, currentMinutes);
  };

  const currentTime = getCurrentTime();

  const date = new Date(startDate);
  const today = new Date();

  const includesToday =
    date.getDate() <= today.getDate() &&
    date.getMonth() <= today.getMonth() &&
    date.getFullYear() <= today.getFullYear();

  const filterPassedTime = (time) => {
    if (!timeFrom) return true;
    const selectedTime = new Date(time);
    const minAllowedTime = getMinTime();
    return selectedTime >= minAllowedTime;
  };

  const minTime = includesToday ? currentTime : undefined;

  const getMinTime = () => {
    if (timeFrom) {
      const selectedTime = new Date(timeFrom);
      // Add 30 minutes to the start time as minimum end time
      const minAllowedTime = new Date(selectedTime.getTime() + 30 * 60000);
      
      // Round up to the next 15-minute interval to align with timeIntervals={15}
      const minutes = minAllowedTime.getMinutes();
      const roundedMinutes = Math.ceil(minutes / 15) * 15;
      const roundedMinAllowedTime = new Date(minAllowedTime);
      roundedMinAllowedTime.setMinutes(roundedMinutes);
      if (roundedMinutes >= 60) {
        roundedMinAllowedTime.setHours(roundedMinAllowedTime.getHours() + 1);
        roundedMinAllowedTime.setMinutes(0);
      }
      
      return roundedMinAllowedTime;
    }
    return academyStartTime || new Date(0, 0, 0, 0, 0);
  };

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = new Date(currentYear, 11, 31); // Set to December 31st of the current year
    return formatDateToYYYYMMDD(lastDate);
  }

  const getTime = (time) => {
    const tempdate = new Date(time);

    const hours = tempdate.getHours(); // Get the hour component (0-23)
    const minutes = tempdate.getMinutes(); // Get the minute component (0-59)

    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;

    return formattedTime;
  };

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const start = new Date(startDate);
    const end = new Date(endDate);

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return count;
  }

  // ### Final Save ###
  const saveHandler = async () => {
    // Prevent double submission
    if (btnIsLoading) return;

    setBtnIsLoading(true);

    // Calculate the effective end date for "Never" option
    let effectiveEndDate = getLastDateOfCurrentYear();

    // If coach has availability end date, use whichever is earlier
    if (coach?.academyAvailability?.endDate) {
      const coachEndDate = new Date(coach.academyAvailability.endDate);
      const yearEndDate = new Date(effectiveEndDate);
      effectiveEndDate = coachEndDate < yearEndDate ? coachEndDate : yearEndDate;
    }

    let lastDateOfYear = formatDateToYYYYMMDD(effectiveEndDate);

    if (sessionType?.name === "Individual") {
      setMaximumGroupSize(1);
    }

    // Proficiency validation
    if (!proficiencyLevel || (Array.isArray(proficiencyLevel) ? proficiencyLevel.length === 0 : proficiencyLevel === "")) {
      setBtnIsLoading(false);
      showToast(
        "Please select proficiency level",
        "You must select a proficiency level before submitting.",
        "warning"
      );
      return;
    }

    if (courseName.length <= 3) {
      setBtnIsLoading(false);
      setCourseNameError(true);
    }

    if (!courseDescription || courseDescription === "<p><br></p>") {
      setCourseDescriptionError(true);
      setBtnIsLoading(false);
    }

    if (!startDate) {
      setBtnIsLoading(false);
      setStartDateError(true);
    }

    if (!selectedDays.length) {
      setBtnIsLoading(false);
      setSelectedDaysError(true);
    }

    if (!timeFrom || !timeTo) {
      if (!timeTo) {
        setTimeToError(true);
      }
      if (!timeFrom) {
        setTimeFromError(true);
      }
      setBtnIsLoading(false);
    }

    // Validate time gap (minimum 30 minutes)
    if (timeFrom && timeTo) {
      const startTime = new Date(timeFrom);
      const endTime = new Date(timeTo);
      
      // Convert to minutes since midnight for accurate comparison
      const startMinutes = startTime.getHours() * 60 + startTime.getMinutes();
      const endMinutes = endTime.getHours() * 60 + endTime.getMinutes();
      const timeDiffInMinutes = endMinutes - startMinutes;

      if (timeDiffInMinutes < 30) {
        setBtnIsLoading(false);
        showToast(
          "Invalid Time Gap",
          "The time gap between start time and end time should be at least 30 minutes.",
          "warning"
        );
        return;
      }
    }

    if (classType === "class") {
      if (fees30 === "" && !(fees60)) {
        setFees30Error(true);
      }
      if (fees60 === "" && !(fees30)) {
        setFees60Error(true);
      }
      if (fees30 === "" || fees60 === "") {
        setBtnIsLoading(false);
      }
    }

    if (classType !== "class") {
      if (price === "") {
        setBtnIsLoading(false);
        setPriceError(true);
      }
    }

    if (!categoryType) {
      setCategoryError(true);
      setBtnIsLoading(false);
    }

    if (!courseFacility) {
      setFacilityError(true);
      setBtnIsLoading(false);
    }

    if (classType === "course" && isItCamp ? !campName : false) {
      setCampNameError(true);
      setBtnIsLoading(false);
    }

    // Validate maximum group size for Group sessions
    if (sessionType === "Group" && classType === "course") {
      const groupSize = Number(maximumGroupSize);
      if (!maximumGroupSize || groupSize < 1 || groupSize > 15) {
        setMaximumGroupSizeError(true);
        setBtnIsLoading(false);
        showToast(
          "Invalid Group Size",
          "Maximum group size must be between 1 and 15."
        );
        return;
      }
    }

    // Validations //
    if (!id) {
      setBtnIsLoading(false);
      showToast(
        "Please select coach, before creating the course",
        "A coach must be selected to create a course.",
        "warning"
      );
      return;
    }
    if (!selectedDays || selectedDays.length === 0) {
      setBtnIsLoading(false);
      showToast(
        "Please select at least one day to continue",
        "You must select at least one day for the course schedule.",
        "warning"
      );
      return;
    }


    if (
      daysDifference(
        new Date(startDate),
        endDate ? new Date(endDate) : new Date(lastDateOfYear),
        selectedDays
      ) === 0
    ) {
      setBtnIsLoading(false);
      showToast(
        "Selected Days is not available between selected date range",
        "Please select different days or adjust the date range.",
        "warning"
      );
      return;
    }

    const startDateTime = `${formatDateToYYYYMMDD(startDate)}T${getTime(
      timeFrom
    )}:00`;
    const endDateTime = `${endDate
        ? formatDateToYYYYMMDD(endDate)
        : formatDateToYYYYMMDD(lastDateOfYear)
      }T${getTime(timeTo)}:00`;

    let isAvailable = await getAvailableSlots(`${id}`);

    if (!isAvailable) {
      setBtnIsLoading(false);
      showToast(
        "Conflicting time slots",
        "The selected time slots conflict with existing courses. Please choose different times.",
        "warning"
      );
      return;
    }

    const selectedUrl = categories.filter((x) => x.name === categoryType);

    // main working body //
    let raw = JSON.stringify({
      courseName: `${courseName.replace(/\s+/g, " ").trim()}`,
      description: `${courseDescription}`,
      images:
        selectedImages.length <= 0
          ? [{ url: selectedUrl[0]?.image }]
          : selectedImages,
      coach_id: `${id}` || "",
      coachName: (coach?.firstName || "") + " " + (coach?.lastName || ""),
      category: `${categoryType}`,
      customImage: selectedImages.length <= 0 ? false : true,

      sessionType:
        classType === "course" ? `${sessionType.toLowerCase()}` : "individual",
      classType: classType,
      camp: isItCamp,
      campName: `${campName.replace(/\s+/g, " ").trim()}`,
      fees: {
        feesCourse: classType !== "class" ? Number(price) : "",
        fees30: classType === "class" ? Number(fees30) : "",
        fees60: classType === "class" ? Number(fees60) : "",
        fees:
          classType === "class"
            ? fees30 && fees60
              ? Number(fees30)
              : fees30 && fees60
                ? Number(fees60)
                : fees30 && fees60
            : Number(price),
      },
      maxGroupSize: maximumGroupSize !== "" ? maximumGroupSize : 1,
      amenitiesProvided: courseAmeneties,
      proficiency: proficiencyLevel,
      facility: { name: courseFacility },
      coachEmail: coach.email,
      dates: {
        startDate: startDateTime,
        endDate: endDateTime,
        startTime: getTime(timeFrom),
        endTime: getTime(timeTo),
        days: selectedDays,
      },
      whatYouHaveToBring: carryThings,
      cancellationPolicy: cancellationPolicy,
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/course`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: raw,
    };

    axios
      .request(config)
      .then((response) => {
        toast({
          title: "Course created successfully",
          status: "success",
          duration: 4000,
          isClosable: true,
          position: "top",
        });
        navigate(`/course-page`);
        setBtnIsLoading(false);
        setCourseDescriptionError(false);
        setCourseName("");
        setCourseNameError(false);
        setCampName("");
        setCampNameError(false);
        setMaximumGroupSize(1);
        setMaximumGroupSizeError(false);
        setCourseDescription("");
        setCourseAmeneties("");
        setStartDateError(false);
        setEndDate("");
        setPriceError(false);
        setFees30("");
        setFees60("");
        setStartDate("");
        setTimeFrom("");
        setSelectedDaysError(false);
        setSelectedDays([]);
        setCancellationPolicy("");
        setCarryThings("");
        setCategoryType({});
        setCourseFacility({});
        setTimeTo("");
        setFees30Error(false);
        setFees60Error(false);
        setSelectedImages([]);
        setCategoryError(false);
        setFacilityError(false);
        setBtnIsLoading(false);
        setSelectedImagesError(false);
      })
      .catch((error) => {
        setBtnIsLoading(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: error.response.data.error,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCategories = async () => {
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/category`
      );
      setCategories(response.data.data);
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const getCoachDetails = () => {
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/${id}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        const coachData = response?.data;

        // Check if KYC documents are available - only check once
        if (!kycChecked && (!coachData?.kycDocuments?.documentImg || coachData.kycDocuments.documentImg.length === 0)) {
          setKycChecked(true);
          showToast(
            "KYC Documents Required",
            "Coach must complete KYC verification before creating courses. Please upload KYC documents first.",
            "error",
            6000
          );
          navigate(`/coach-page/details/${id}`);
          return;
        }

        setFacilities(coachData?.linkedFacilities);
        setCoach(coachData);

        if (coachData?.academyAvailability) {
          const { startTime, endTime } = coachData.academyAvailability;
          if (startTime && endTime) {
            const [sh, sm] = startTime.split(":");
            const [eh, em] = endTime.split(":");

            // Create proper Date objects for today's date with the specified times
            const today = new Date();
            const startTimeObj = new Date(today.getFullYear(), today.getMonth(), today.getDate(), Number(sh), Number(sm) || 0);
            const endTimeObj = new Date(today.getFullYear(), today.getMonth(), today.getDate(), Number(eh), Number(em) || 0);

            setAcademyStartTime(startTimeObj);
            setAcademyEndTime(endTimeObj);

          }
        }
      })
      .catch((error) => {
        console.log(error);
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleRadioChange = (e) => {
    const value = e.target.value === "yes";
    setIsItCamp(value);
  };

  // Fix for the time picker filters
  const filterTimeForStart = (time) => {
    const selectedTime = new Date(time);

    // If start date is today, don't allow past times
    if (startDate) {
      const today = new Date();
      const isToday = startDate.toDateString() === today.toDateString();
      if (isToday) {
        const currentTime = new Date();
        currentTime.setSeconds(0, 0); // Reset seconds and milliseconds for accurate comparison
        if (selectedTime < currentTime) {
          return false;
        }
      }
    }

    // If academy times aren't set, allow any time (after current time if today)
    if (!academyStartTime || !academyEndTime) {
      return true;
    }

    // Convert academy times to Date objects for comparison
    const academyStart = new Date(academyStartTime);
    const academyEnd = new Date(academyEndTime);

    // If start date is today, only check current time and academy hours
    if (startDate) {
      const today = new Date();
      const isToday = startDate.toDateString() === today.toDateString();
      if (isToday) {
        const currentTime = new Date();
        currentTime.setSeconds(0, 0);
        // Must be after current time AND within academy hours
        return selectedTime >= currentTime && selectedTime >= academyStart && selectedTime <= academyEnd;
      }
    }

    // For future dates, allow any time within academy hours
    return selectedTime >= academyStart && selectedTime <= academyEnd;
  };

  const filterTimeForEnd = (time) => {
    // Convert time to minutes since midnight for easier comparison
    const selectedTime = new Date(time);
    const selectedMinutes = selectedTime.getHours() * 60 + selectedTime.getMinutes();
  
    // If no start time is selected, allow any time within academy hours
    if (!timeFrom) {
      if (!academyStartTime || !academyEndTime) {
        return true;
      }
      const academyStartMinutes = academyStartTime.getHours() * 60 + academyStartTime.getMinutes();
      const academyEndMinutes = academyEndTime.getHours() * 60 + academyEndTime.getMinutes();
      return selectedMinutes >= academyStartMinutes && selectedMinutes <= academyEndMinutes;
    }
  
    // Calculate 30-minute minimum from start time
    const startTime = new Date(timeFrom);
    const startMinutes = startTime.getHours() * 60 + startTime.getMinutes();
    const minAllowedMinutes = startMinutes + 30;
    
    // Round up to the next 15-minute interval
    const roundedMinAllowedMinutes = Math.ceil(minAllowedMinutes / 15) * 15;
    
  
    // If academy times aren't set, only check 30-minute minimum (rounded to 15-min intervals)
    if (!academyStartTime || !academyEndTime) {
      return selectedMinutes >= roundedMinAllowedMinutes;
    }
  
    // Convert academy times to minutes for comparison
    const academyStartMinutes = academyStartTime.getHours() * 60 + academyStartTime.getMinutes();
    const academyEndMinutes = academyEndTime.getHours() * 60 + academyEndTime.getMinutes();
    
    // Use the later of: 30 minutes after start time (rounded) OR academy start time
    const effectiveMinMinutes = Math.max(roundedMinAllowedMinutes, academyStartMinutes);
    
    return selectedMinutes >= effectiveMinMinutes && selectedMinutes <= academyEndMinutes;
  };
  // Update your DatePicker components like this:

  function getLastDayOfYear() {
    let currentDate = new Date();
    currentDate.setMonth(11);
    currentDate.setDate(31);
    return currentDate.toISOString();
  }

  const getAvailableSlots = async (id, value, endTime) => {
    // Calculate the effective end date for "Never" option
    let effectiveEndDate = getLastDateOfCurrentYear();

    // If coach has availability end date, use whichever is earlier
    if (coach?.academyAvailability?.endDate) {
      const coachEndDate = new Date(coach.academyAvailability.endDate);
      const yearEndDate = new Date(effectiveEndDate);
      effectiveEndDate = coachEndDate < yearEndDate ? coachEndDate : yearEndDate;
    }

    let lastDateOfYear = formatDateToYYYYMMDD(effectiveEndDate);
    const startDateTime = `${formatDateToYYYYMMDD(startDate)}T${getTime(
      timeFrom
    )}:00`;
    const endDateTime = `${endDate ? formatDateToYYYYMMDD(endDate) : lastDateOfYear
      }T${timeTo !== "" ? getTime(timeTo) : getTime(endTime)}:00`;

    let data = JSON.stringify({
      dates: {
        startDate: startDateTime,
        endDate: endDateTime,
        startTime: getTime(timeFrom),
        endTime: endTime ? getTime(endTime) : getTime(timeTo),
        days: selectedDays,
      },
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/course/availableSlots/${id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    let response = await axios.request(config);

    setConflictResult(response.data);
    if (response.data.message === "Slots available") {
      setShowSlotConflict(false);
      return true;
    } else {
      setShowSlotConflict(true);
      return false;
    }
  };

  const formatSlotDate = (date) => {
    const originalDateString = date;
    const originalDate = new Date(originalDateString);

    const year = originalDate.getUTCFullYear();
    const month = originalDate.getUTCMonth() + 1;
    const day = originalDate.getUTCDate();

    const formattedDateString = `${day}-${month < 10 ? "0" : ""
      }${month}-${year}`;

    return formattedDateString;
  };

  const handleFileChange = async (e) => {
    try {
      const file = e.currentTarget.files[0];
      setSelectedImagesError(false);

      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB.",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        toast({
          title: "Image Uploaded Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title:
            "Something went wrong while uploading image, please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }

      setSelectedImages([...selectedImages, { url: url }]);
    } catch (error) {
      toast({
        title:
          "Something went wrong while uploading image, please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const deleteImageFiles = async (url, index) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      toast({
        title: "Image removed Successfully",
        status: "success",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } catch (error) {
      console.log(error);
      toast({
        title:
          "Something went wrong while removing image, please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    getCategories();
    getCoachDetails();
  }, []);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Seo
        title="Create Training Schedule | KhelSport"
        description="Create new training schedules and courses. Set up coaching sessions, manage pricing, and configure course details."
        canonical={`https://khelcoach.com/courses/create/${id || ''}`}
        url={`https://khelcoach.com/courses/create/${id || ''}`}
        jsonLd={schema.course({
          name: 'Create Training Schedule',
          description: 'Create new training schedules and courses on KhelSport platform',
          url: `https://khelcoach.com/courses/create/${id || ''}`,
          provider: {
            name: 'KhelSport',
            sameAs: ['https://khelcoach.com']
          }
        })}
      />
      <Layout title="Create Training Schedule">
        {/* Breadcrumb */}
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize="sm">

              <BreadcrumbItem>
                <Link to="/course-page">Training Schedule</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Create Training Schedule</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
        </Flex>
        <Card>
          <CardBody>
            {/* Course name */}
            <FormControl isInvalid={courseNameError}>
              <FormLabel>Training Schedule Name</FormLabel>
              <Input
                type="text"
                placeholder="Enter training schedule name"
                name="course-name"
                id="course-name"
                min={10}
                value={courseName}
                autoComplete="off"
                onChange={(e) => {
                  setCourseName(e.target.value);
                  if (e.target.value !== "") {
                    setCourseNameError(false);
                  } else {
                    setCourseNameError(true);
                  }
                  if (
                    e.target.value.length > 100 ||
                    e.target.value.length < 2
                  ) {
                    setCourseNameError(true);
                  } else {
                    setCourseNameError(false);
                  }
                }}
              />
              {courseNameError && (
                <StyledFormErrorMessage>
                  Please enter training schedule name, atleast 3 characters and maximum
                  100 characters
                </StyledFormErrorMessage>
              )}
            </FormControl>
            {/* Description */}
            <FormControl mt={6} isInvalid={courseDescriptionError}>
              <FormLabel>Description</FormLabel>
              <ReactQuill
                theme="snow"
                value={courseDescription}
                onChange={(value) => {
                  setCourseDescription(value);
                  if (value === "" || value === "<p><br></p>") {
                    setCourseDescriptionError(true);
                  } else {
                    setCourseDescriptionError(false);
                  }
                }}
              />
              {courseDescriptionError && (
                <StyledFormErrorMessage>
                  Please enter description
                </StyledFormErrorMessage>
              )}
            </FormControl>
          </CardBody>
        </Card>
        <Card mt={4}>
          <CardBody>
            {/* Course Image */}
            <FormControl mt={6}>
              <FormLabel>Course Image</FormLabel>
              <Box mt={3}>
                <Input
                  id={`kycDocuments.documentImg.${0}.url`}
                  name={`kycDocuments.documentImg.${0}.url`}
                  type="file"
                  accept="image/*"
                  disabled={selectedImages.length >= 10}
                  onChange={(e) => handleFileChange(e, 0)}
                />
                {selectedImagesError && (
                  <Text color={"red.500"} fontSize={"sm"} mt={1}>
                    Please select atleast one document image
                  </Text>
                )}
                {selectedImages.length >= 10 && (
                  <Text color={"orange.500"} fontSize={"sm"} mt={1}>
                    Maximum 10 images allowed
                  </Text>
                )}
                {selectedImages.length > 0 && (
                  <Flex
                    wrap={"wrap"}
                    mt={3}
                    gap={2}
                    justifyContent={{ base: "center", md: "flex-start" }}
                  >
                    {selectedImages.map((preview, index) => (
                      <Box
                        key={index}
                        textAlign={"center"}
                        border="1px solid"
                        borderColor="gray.200"
                        borderRadius="md"
                        p={2}
                        bg="gray.50"
                      >
                        <a
                          href={`${preview?.url}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Image
                            src={preview?.url}
                            alt={`Preview ${index}`}
                            height={isMobile ? "80px" : "120px"}
                            width={isMobile ? "80px" : "120px"}
                            objectFit="cover"
                            borderRadius="md"
                            border="1px solid"
                            borderColor="gray.300"
                          />
                        </a>
                        <Button
                          colorScheme="red"
                          size="sm"
                          mt={1}
                          onClick={() => {
                            deleteImageFiles(preview?.url, index);
                            const updatedImages = selectedImages.filter(
                              (image) => image.url !== preview.url
                            );
                            setSelectedImages(updatedImages);

                            // Reset file input when all images are removed
                            if (updatedImages.length === 0) {
                              const fileInput = document.getElementById(`kycDocuments.documentImg.${0}.url`);
                              if (fileInput) {
                                fileInput.value = '';
                              }
                            }
                          }}
                        >
                          Remove
                        </Button>
                      </Box>
                    ))}
                  </Flex>
                )}
              </Box>
            </FormControl>
          </CardBody>
        </Card>

        {/*  Slot */}
        <Card mt={4}>
          <CardBody>
            {/* Radion btn - session type */}
            <Flex justifyContent={"center"} alignItems={"center"}>
              <Stack direction="row">
                <Box mr={6}>
                  <input
                    id="notification-method-class"
                    type="radio"
                    value=""
                    required
                    checked={classType === "class"}
                    onChange={(e) => {
                      e.target.checked && setClassType("class");
                      endDate ? setIsEnd(true) : setIsEnd(false);
                    }}
                  />
                  <label
                    htmlFor="notification-method-class"
                    className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                    style={{
                      cursor: "pointer",
                      fontSize: "1.1rem",
                      fontWeight: "600",
                    }}
                  >
                    Session
                  </label>
                </Box>
                <Box>
                  <input
                    id="notification-method-course"
                    type="radio"
                    value=""
                    required
                    checked={classType === "course"}
                    onChange={(e) => {
                      e.target.checked && setClassType("course");
                      setIsEnd(true);
                    }}
                  />
                  <label
                    htmlFor="notification-method-course"
                    className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                    style={{
                      cursor: "pointer",
                      fontSize: "1.1rem",
                      fontWeight: "600",
                    }}
                  >
                    Course
                  </label>
                </Box>
              </Stack>
            </Flex>
            {/* Start Date */}
            <FormControl my={3} isInvalid={startDateError}>
              <FormLabel>Start Date</FormLabel>
              {coach?.academyAvailability?.startDate && coach?.academyAvailability?.endDate && (
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Available from: {new Date(coach.academyAvailability.startDate).toLocaleDateString()} to {new Date(coach.academyAvailability.endDate).toLocaleDateString()}
                </Text>
              )}
              <DatePicker
                placeholderText="Select start date"
                className={
                  startDateError
                    ? "my-custom-datepicker dateError"
                    : "my-custom-datepicker"
                }
                selected={startDate}
                onKeyDown={(e) => e.preventDefault()}
                onChange={(date) => {
                  setEndDate("");
                  setStartDate(date);
                  if (date !== "") {
                    setStartDateError(false);
                  } else {
                    setStartDateError(true);
                  }
                }}
                minDate={dateRestrictions.minDate}
                maxDate={dateRestrictions.maxDate}
                dateFormat="dd MMMM yyyy"
                filterDate={(date) => {
                  const today = new Date();
                  today.setHours(0, 0, 0, 0);
                  return date >= today;
                }}
              />
              {startDateError && (
                <StyledFormErrorMessage>
                  Please select start date
                </StyledFormErrorMessage>
              )}
            </FormControl>
            {/* Select Days */}
            <FormControl my={4} isInvalid={selectedDaysError}>
              <FormLabel>Select Days</FormLabel>
              {coach?.academyAvailability?.days && coach.academyAvailability.days.length > 0 && (
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Available days: {coach.academyAvailability.days.join(", ")}
                </Text>
              )}

              <Flex
                wrap="wrap"
                justifyContent={{ base: "center", md: "flex-start" }}
                gap={2}
                maxW="100%"
                overflow="hidden"
              >
                {days.map((day, i) => {
                  const isAvailable = availableDays.includes(day);
                  return (
                    <Button
                      key={i}
                      type="button"
                      onClick={() => {
                        if (!isAvailable) return; // Don't allow selection of unavailable days

                        setSelectedDays(
                          selectedDays.includes(day)
                            ? selectedDays.filter(
                              (selected) => selected !== day
                            )
                            : [...selectedDays, day]
                        );
                        if (!day) {
                          setSelectedDaysError(true);
                        } else {
                          setSelectedDaysError(false);
                        }
                      }}
                      colorScheme={
                        selectedDays.includes(day) ? "telegram" : "gray"
                      }
                      size={isMobile ? "sm" : "md"}
                      minW={isMobile ? "45px" : "60px"}
                      flex={isMobile ? "1 1 auto" : "none"}
                      maxW={isMobile ? "calc(14.28% - 4px)" : "none"}
                      isDisabled={!isAvailable}
                      opacity={isAvailable ? 1 : 0.4}
                      cursor={isAvailable ? "pointer" : "not-allowed"}
                      _hover={isAvailable ? {} : { bg: "gray.200" }}
                    >
                      {day}
                    </Button>
                  );
                })}
              </Flex>
              <StyledFormErrorMessage>
                {selectedDaysError ? "Select the Days" : ""}
              </StyledFormErrorMessage>
            </FormControl>
            <Divider />
            {/* End Date - radio btn */}

            <Box>
              {classType === "class" && (
                <FormLabel mt={3}>End Date</FormLabel>
              )}
              <Flex
                justifyContent={"flex-start"}
                alignItems={"center"}
                mb={3}
              >
                {classType === "class" && (
                  <Stack direction="row">
                    <Box mr={6}>
                      <input
                        id="horizontal-list-radio-license-never"
                        type="radio"
                        required
                        value=""
                        defaultChecked={!isEnd}
                        onChange={(e) => {
                          e.target.checked
                            ? setIsEnd(false)
                            : setIsEnd(true);
                        }}
                        name="list-radio"
                      />
                      <label
                        htmlFor="horizontal-list-radio-license-never"
                        style={{
                          marginLeft: "8px",
                          cursor: "pointer",
                          fontSize: "1rem",
                          fontWeight: "600",
                        }}
                      >
                        Never
                      </label>
                    </Box>
                    <Box>
                      <input
                        id="horizontal-list-radio-license-on"
                        type="radio"
                        required
                        value=""
                        defaultChecked={isEnd}
                        onChange={(e) => {
                          e.target.checked
                            ? setIsEnd(true)
                            : setIsEnd(false);
                        }}
                        name="list-radio"
                      />
                      <label
                        htmlFor="horizontal-list-radio-license-on"
                        style={{
                          marginLeft: "8px",
                          cursor: "pointer",
                          fontSize: "1rem",
                          fontWeight: "600",
                        }}
                      >
                        On
                      </label>
                    </Box>
                  </Stack>
                )}
              </Flex>
              {/* when end date is on*/}
              {isEnd && (
                <FormControl my={3}>
                  <FormLabel>Select End Date</FormLabel>
                  {/* <Input
                        type="date"
                        placeholder="Select start date"
                        id="start_date"
                        onChange={(e) => setEndDate(e.target.value)}
                        required
                        minDate={new Date()}
                      /> */}
                  <DatePicker
                    placeholderText="Select end date"
                    className={
                      startDateError
                        ? "my-custom-datepicker dateError"
                        : "my-custom-datepicker"
                    }
                    selected={endDate}
                    onKeyDown={(e) => e.preventDefault()}
                    onChange={(date) => {
                      setEndDate(date);
                    }}
                    minDate={
                      startDate
                        ? new Date(startDate).setDate(
                          new Date(startDate).getDate()
                        )
                        : dateRestrictions.minDate
                    }
                    maxDate={dateRestrictions.maxDate}
                    dateFormat="dd MMMM yyyy"
                  />
                </FormControl>
              )}
            </Box>
            {/* Start Time && End time */}
            <Divider />

            {coach?.academyAvailability?.startTime && coach?.academyAvailability?.endTime && (
              <Text fontSize="sm" color="gray.600" mb={2}>
                Available start time: {(() => {
                  const time = coach.academyAvailability.startTime;
                  const [hours, minutes] = time.split(':');
                  const hour12 = hours % 12 || 12;
                  const ampm = hours >= 12 ? 'PM' : 'AM';
                  return `${hour12}:${minutes} ${ampm}`;
                })()} & Available end time: {(() => {
                  const time = coach.academyAvailability.endTime;
                  const [hours, minutes] = time.split(':');
                  const hour12 = hours % 12 || 12;
                  const ampm = hours >= 12 ? 'PM' : 'AM';
                  return `${hour12}:${minutes} ${ampm}`;
                })()}
              </Text>
            )}

            <Flex justifyContent={"space-between"} alignItems={"center"}>

              <FormControl
                my={3}
                flexBasis={"48%"}
                isInvalid={timeFromError}
              >
                <FormLabel>Start Time</FormLabel>
                <DatePicker
                  selected={timeFrom}
                  className={
                    timeFromError
                      ? "my-custom-datepicker dateError"
                      : "my-custom-datepicker"
                  }
                  onKeyDown={(e) => e.preventDefault()}
                  onChange={async (value) => {
                    if (value === "") {
                      setTimeFromError(true);
                    } else {
                      setTimeFromError(false);
                    }
                    setTimeFrom(value);
                    setTimeTo("");
                  }}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  timeCaption="Time"
                  dateFormat="h:mm aa"
                  placeholderText="Select time"
                  disabled={startDate === ""}
                  filterTime={filterTimeForStart}
                  timeClassName={(time) =>
                    filterTimeForStart(time) ? "enabled-time" : "disabled-time"
                  }
                />


              </FormControl>
              <FormControl my={3} flexBasis={"48%"} isInvalid={timeToError}>
                <FormLabel>End Time</FormLabel>
                <DatePicker
                  key={timeFrom ? timeFrom.toString() : 'no-start-time'}
                  selected={timeTo}
                  onKeyDown={(e) => e.preventDefault()}
                  onChange={async (value) => {
                    await getAvailableSlots(`${id}`, "", value);
                    setTimeTo(value);
                    if (value === "") {
                      setTimeToError(true);
                    } else {
                      setTimeToError(false);
                    }
                  }}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  timeCaption="Time"
                  dateFormat="h:mm aa"
                  className={
                    timeToError
                      ? "my-custom-datepicker dateError"
                      : "my-custom-datepicker"
                  }
                  placeholderText="Select time"
                  disabled={timeFrom === ""}
                  filterTime={filterTimeForEnd}
                  timeClassName={(time) =>
                    filterTimeForEnd(time) ? "enabled-time" : "disabled-time"
                  }
                />
                {timeToError && (
                  <StyledFormErrorMessage>Select the time</StyledFormErrorMessage>
                )}
              </FormControl>
            </Flex>
            {/* Conflicting Dates */}
            {showSlotConflict && (
              <Box my={4}>
                <Flex
                  justifyContent={"center"}
                  alignItems={"center"}
                  mb={2}
                >
                  <Text
                    color={"red.500"}
                    fontSize={"lg"}
                    fontWeight={"semibold"}
                  >
                    Conflicting Courses
                  </Text>
                </Flex>
                <TableContainer>
                  <Table variant="simple">
                    <Thead
                      bgColor={"#c1eaee"}
                      position={"sticky"}
                      top={"0px"}
                    >
                      <Tr bgColor={"red.100"}>
                        <Th>Start Date</Th>
                        <Th>End Date</Th>
                        <Th>Start Time</Th>
                        <Th>End Time</Th>
                        <Th>Days</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {conflictResult?.conflictingDates?.map(
                        (conflictDate, inx) => {
                          return (
                            <Tr bgColor={"gray.50"} key={inx}>
                              <Td>
                                {formatSlotDate(conflictDate?.startDate)}
                              </Td>
                              <Td>
                                {formatSlotDate(conflictDate?.endDate)}
                              </Td>
                              <Td>{conflictDate?.conflictingStartTime}</Td>
                              <Td>{conflictDate?.conflictingEndTime}</Td>
                              <Td>
                                <UnorderedList>
                                  {conflictDate?.conflictingDays?.map(
                                    (days, i) => {
                                      return (
                                        <ListItem key={i}>{days}</ListItem>
                                      );
                                    }
                                  )}
                                </UnorderedList>
                              </Td>
                            </Tr>
                          );
                        }
                      )}
                    </Tbody>
                  </Table>
                </TableContainer>
              </Box>
            )}
            {/* price */}
            {classType === "class" ? (
              <>
                <Flex
                  justifyContent={"space-between"}
                  alignItems={"center"}
                >
                  <FormControl
                    my={4}
                    flexBasis={"30%"}
                    isInvalid={fees30Error}
                  >
                    <FormLabel>
                      Price <span>(30 minutes)</span>
                    </FormLabel>
                    <Input
                      type="number"
                      name="price"
                      id="price"
                      placeholder="₹0"
                      value={fees30}
                      autoComplete="off"
                      onChange={(e) => {
                        let newValue = e.target.value;
                        newValue = newValue.replace(/[^\d.]/g, "");
                        newValue = newValue.replace(/^0+/, "");
                        newValue = newValue.replace(/^\./, "");
                        if (newValue.includes(".")) {
                          newValue = newValue.split(".")[0];
                        }
                        setFees30(newValue);
                        if (e.target.value === "" && !(fees60)) {
                          setFees30Error(true);
                        } else {
                          setFees60Error(false);
                          setFees30Error(false);
                        }
                      }}
                    />
                    {fees30Error && (
                      <StyledFormErrorMessage>
                        Please enter the price
                      </StyledFormErrorMessage>
                    )}
                  </FormControl>
                  <FormControl
                    my={4}
                    flexBasis={"30%"}
                    isInvalid={fees60Error}
                  >
                    <FormLabel>
                      Price <span>(60 minutes)</span>
                    </FormLabel>
                    <Input
                      type="number"
                      name="price"
                      id="price"
                      placeholder="₹0"
                      min={0}
                      step={1}
                      value={fees60}
                      autoComplete="off"
                      onChange={(e) => {
                        let newValue = e.target.value;
                        newValue = newValue.replace(/[^\d.]/g, "");
                        newValue = newValue.replace(/^0+/, "");
                        newValue = newValue.replace(/^\./, "");
                        if (newValue.includes(".")) {
                          newValue = newValue.split(".")[0];
                        }
                        setFees60(newValue);
                        if (e.target.value === "" && !(fees30)) {
                          setFees60Error(true);
                        } else {
                          setFees60Error(false);
                          setFees30Error(false);

                        }
                      }}
                    />
                    {fees60Error && (
                      <StyledFormErrorMessage>
                        Please enter price
                      </StyledFormErrorMessage>
                    )}
                  </FormControl>
                </Flex>
              </>
            ) : (
              <FormControl my={4} isInvalid={priceError}>
                <FormLabel>Price</FormLabel>
                <Input
                  type="number"
                  name="price"
                  id="price"
                  placeholder="₹0.00"
                  value={price}
                  autoComplete="off"
                  onChange={(e) => {
                    let newValue = e.target.value;
                    newValue = newValue.replace(/[^\d.]/g, "");
                    newValue = newValue.replace(/^0+/, "");
                    newValue = newValue.replace(/^\./, "");
                    if (newValue.includes(".")) {
                      newValue = newValue.split(".")[0];
                    }
                    setPrice(newValue);
                    if (e.target.value !== "") {
                      setPriceError(false);
                    } else {
                      setPriceError(true);
                    }
                  }}
                />
                {priceError && (
                  <StyledFormErrorMessage>
                    Please enter the price
                  </StyledFormErrorMessage>
                )}
              </FormControl>
            )}
          </CardBody>
        </Card>
        <Card mt={4}>
          <CardBody>
            {/* Session type if -- Course */}
            {classType === "course" && (
              <FormControl mt={6}>
                <FormLabel>Session Type</FormLabel>
                <Select
                  value={sessionType}
                  onChange={(e) => {
                    setSessionType(e.target.value);
                    setMaximumGroupSize(1);
                  }}
                >
                  <option value="Group">Group</option>
                  <option value="Individual">Individual</option>
                </Select>
              </FormControl>
            )}

            {/* Is it a camp if --- Course */}
            {classType !== "class" && (
              <>
                <Flex
                  justifyContent={"flex-start"}
                  alignItems={"center"}
                  mt={4}
                  mb={2}
                >
                  <Text
                    fontWeight={"semibold"}
                    fontSize={"16px"}
                    mb={1}
                    mr={4}
                  >
                    Is it a camp ?{" "}
                  </Text>
                  <Box mr={4}>
                    <input
                      type="radio"
                      id="yes"
                      name="camp-type"
                      value="yes"
                      onChange={handleRadioChange}
                      style={{ marginTop: "2px" }}
                    />
                    <label
                      htmlFor="yes"
                      style={{ marginLeft: "8px", cursor: "pointer" }}
                    >
                      Yes
                    </label>
                  </Box>
                  <Box>
                    <input
                      type="radio"
                      id="no"
                      name="camp-type"
                      value="no"
                      onChange={handleRadioChange}
                      defaultChecked
                      style={{ marginTop: "2px" }}
                    />
                    <label
                      htmlFor="no"
                      style={{ marginLeft: "8px", cursor: "pointer" }}
                    >
                      No
                    </label>
                  </Box>
                </Flex>
                {isItCamp && (
                  <FormControl isInvalid={campNameError}>
                    <Input
                      type="text"
                      name="camp-name"
                      id="camp-name"
                      value={campName}
                      autoComplete="off"
                      placeholder="Enter camp name"
                      onChange={(e) => {
                        setCampName(e.target.value);
                        if (e.target.value !== "") {
                          setCampNameError(false);
                        } else {
                          setCampNameError(true);
                        }
                        if (
                          e.target.value.length > 100 ||
                          e.target.value.length < 3
                        ) {
                          setCampNameError(true);
                        } else {
                          setCampNameError(false);
                        }
                      }}
                    />
                    {campNameError && (
                      <StyledFormErrorMessage>
                        Please enter camp name characters must be greater
                        than or equal to 3 and less than equal to 100
                      </StyledFormErrorMessage>
                    )}
                  </FormControl>
                )}
              </>
            )}

            {/* Category */}
            <FormControl mt={6} isInvalid={categoryError}>
              <FormLabel>Category</FormLabel>
              <Select
                placeholder="Select Category"
                onChange={(e) => {
                  setCategoryType(e.target.value);
                  setCategoryError(false);
                }}
              >
                {categories?.map((category, inx) => (
                  <option key={inx} value={category?.name}>
                    {category?.name}
                  </option>
                ))}
              </Select>
              {categoryError && (
                <StyledFormErrorMessage>Please select category</StyledFormErrorMessage>
              )}
            </FormControl>
            {/* Select Facility */}
            <FormControl mt={6} isInvalid={facilityError}>
              <FormLabel>Facility</FormLabel>
              <Select
                placeholder="Select Facility"
                onChange={(e) => {
                  setCourseFacility(e?.target?.value);
                  setFacilityError(false);
                  setCourseAmeneties(
                    facilities.filter((x) => x.name === e.target.value)[0]
                      .amenities
                  );
                }}
              >
                {facilities.map((facility, inx) => (
                  <option key={inx} value={facility.name}>
                    {facility.name}
                  </option>
                ))}
              </Select>
              {facilityError && (
                <StyledFormErrorMessage>Please select facility</StyledFormErrorMessage>
              )}
            </FormControl>

            {/* Max group size */}
            {sessionType === "Group" && classType === "course" && (
              <FormControl mt={6} isInvalid={maximumGroupSizeError}>
                <FormLabel>Maximum Group Size (1-15)</FormLabel>
                <Input
                  type="number"
                  placeholder="Enter maximum group size (1-15)"
                  name="max-group-size"
                  id="max-group-size"
                  value={maximumGroupSize}
                  min="1"
                  max="15"
                  onChange={(e) => {
                    const value = e.target.value;
                    const numValue = parseInt(value, 10);

                    // Allow empty value for user to type
                    if (value === "") {
                      setMaximumGroupSize("");
                      setMaximumGroupSizeError(true);
                      return;
                    }

                    // Prevent non-numeric input
                    if (isNaN(numValue)) {
                      return;
                    }

                    // Enforce range 1-15
                    if (numValue < 1) {
                      setMaximumGroupSize(1);
                      setMaximumGroupSizeError(false);
                    } else if (numValue > 15) {
                      setMaximumGroupSize(15);
                      setMaximumGroupSizeError(false);
                    } else {
                      setMaximumGroupSize(numValue);
                      setMaximumGroupSizeError(false);
                    }
                  }}
                  onBlur={(e) => {
                    // Ensure value is set to 1 if empty on blur
                    if (e.target.value === "" || parseInt(e.target.value, 10) < 1) {
                      setMaximumGroupSize(1);
                      setMaximumGroupSizeError(false);
                    }
                  }}
                  onKeyDown={(e) => {
                    // Prevent entering 0, negative numbers, and decimal points
                    if (e.key === '0' && e.target.value === '') {
                      e.preventDefault();
                    }
                    if (e.key === '-' || e.key === '.' || e.key === 'e' || e.key === 'E') {
                      e.preventDefault();
                    }
                  }}
                />
                {maximumGroupSizeError && (
                  <StyledFormErrorMessage>
                    Please enter maximum group size between 1 and 15
                  </StyledFormErrorMessage>
                )}
              </FormControl>
            )}
            {/* Proficiency Level */}

            <FormControl mt={6}>
              <FormLabel>Proficiency Level</FormLabel>
              <Box position="relative" id="proficiency-menu">
                <Button
                  w={"100%"}
                  rightIcon={<FaChevronDown />}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setIsProficiencyMenuOpen(!isProficiencyMenuOpen);
                  }}
                >
                  {proficiencyLevel.length > 0
                    ? `${proficiencyLevel.length} selected`
                    : "Select Proficiency Level(s)"}
                </Button>
                
                {isProficiencyMenuOpen && (
                  <Box
                    position="absolute"
                    top="100%"
                    left={0}
                    right={0}
                    bg="white"
                    border="1px solid"
                    borderColor="gray.200"
                    borderRadius="md"
                    boxShadow="lg"
                    zIndex={1000}
                    mt={1}
                    minW={window.innerWidth - 600 + "px"}
                  >
                    <Box p={2}>
                      {proficiencyLevelOption?.map((proficiency, i) => (
                        <Box p={2} key={i}>
                          <Checkbox
                            size="md"
                            colorScheme="green"
                            value={proficiency.value}
                            isChecked={proficiencyLevel.includes(proficiency.value)}
                            onChange={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              const isChecked = e.target.checked;
                              const value = proficiency.value;
                              
                              if (isChecked) {
                                // Add to proficiency level if not already present
                                if (!proficiencyLevel.includes(value)) {
                                  const newLevels = [...proficiencyLevel, value];
                                  setProficiencyLevel(newLevels);
                                }
                              } else {
                                // Remove from proficiency level
                                const newLevels = proficiencyLevel.filter(level => level !== value);
                                setProficiencyLevel(newLevels);
                              }
                            }}
                          >
                            {proficiency.name}
                          </Checkbox>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                )}
              </Box>

              <HStack spacing={4} mt={2} flexWrap="wrap">
                {proficiencyLevel && proficiencyLevel.length > 0 &&
                  proficiencyLevel.map((level, index) => {
                    const levelOption = proficiencyLevelOption.find(opt => opt.value === level);
                    return (
                      <Tag
                        key={index}
                        size={"md"}
                        variant="solid"
                        colorScheme="teal"
                        mb={1}
                      >
                        {levelOption?.name || level}
                      </Tag>
                    );
                  })
                }
              </HStack>
            </FormControl>

            {/* Ameneties */}
            <FormControl mt={6}>
              <FormLabel>Amenities</FormLabel>
              <ReactQuill
                theme="snow"
                value={courseAmeneties}
                onChange={(value) => {
                  setCourseAmeneties(value);
                }}
              />
            </FormControl>
            {/* Thing Have to Carry with */}
            <FormControl mt={6}>
              <FormLabel>Thing Have to Carry with</FormLabel>
              <ReactQuill
                theme="snow"
                value={carryThings}
                onChange={(value) => {
                  setCarryThings(value);
                }}
              />
            </FormControl>
            {/* Cancellation Policy */}
            <FormControl mt={6}>
              <FormLabel>Cancellation Policy</FormLabel>
              <ReactQuill
                theme="snow"
                value={cancellationPolicy}
                onChange={(value) => {
                  setCancellationPolicy(value);
                }}
              />
            </FormControl>
          </CardBody>
        </Card>
        <Flex
          mt={4}
          justifyContent={"space-between"}
          alignItems={"center"}
          gap={4}
          direction={{ base: "column", md: "row" }}
        >
          <Button
            variant={"solid"}
            colorScheme="red"
            size={isMobile ? "lg" : "md"}
            px={8}
            flex={{ base: "1", md: "0 0 49%" }}
            onClick={onOpen3}
            disabled={btnIsLoading}
            height={isMobile ? "48px" : "40px"}
            minHeight={isMobile ? "48px" : "40px"}
            _disabled={{
              opacity: 0.6,
              cursor: "not-allowed",
            }}
          >
            Discard
          </Button>
          <Button
            variant={"solid"}
            colorScheme="green"
            size={isMobile ? "lg" : "md"}
            px={8}
            ml={{ base: 0, md: 4 }}
            isLoading={btnIsLoading}
            loadingText="Submitting..."
            onClick={saveHandler}
            flex={{ base: "1", md: "0 0 49%" }}
            disabled={btnIsLoading}
            height={isMobile ? "48px" : "40px"}
            minHeight={isMobile ? "48px" : "40px"}
            _disabled={{
              opacity: 0.6,
              cursor: "not-allowed",
            }}
            // iOS specific fixes
            _focus={{
              boxShadow: "none",
            }}
            _active={{
              transform: "none",
            }}
            style={{
              WebkitAppearance: "none",
              WebkitTapHighlightColor: "transparent",
            }}
          >
            Submit
          </Button>
        </Flex>

        {/* Discard */}
        <AlertDialog isOpen={isOpen3} onClose={onClose3} isCentered>
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Discard Changes
              </AlertDialogHeader>

              <AlertDialogBody>
                Are you sure? You can't undo this action afterwards.
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  colorScheme="red"
                  onClick={() => {
                    onClose3();
                  }}
                >
                  No
                </Button>
                <Button
                  colorScheme="green"
                  onClick={() => {
                    onClose3();
                    navigate(-1);
                  }}
                  ml={3}
                >
                  Yes
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Layout>
    </Box>
  );
};

export default CourseCreation;
