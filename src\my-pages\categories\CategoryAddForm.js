import React, { useState, useEffect } from "react";
import { Form, useFormik } from "formik";
import * as Yup from "yup";
import Layout from "../../layout/default";
import {
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Button,
  VStack,
  useToast,
  Card,
  CardBody,
  Box,
  Image,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Textarea,
  Tooltip,
  Text,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";
import { IoMdArrowRoundBack } from "react-icons/io";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const CategoryAddForm = () => {
  const toast = useToast();
  const navigate = useNavigate();

  const [image, setImage] = useState("");
  const [imagePrev, setImagePrev] = useState("");
  const [submitBtnLoading, setSubmitBtnLoading] = useState(false);
  const [btnDisable, setBtnDisable] = useState(false);
  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];
  function restrictSpace(event) {
    if (event.keyCode === 32) {
      event.preventDefault();
    }
  }

  const formik = useFormik({
    initialValues: {
      name: "",
      description: "",
      image: "",
      handle: ""
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().min(3).required("Name is required"),
      image: Yup.string().min(3).required(" Please select the image."),
      description: Yup.string().min(10),
      handle: Yup.string().min(3).required("Handle  is required"),
    }),
    onSubmit: async (values, { resetForm }) => {
      setSubmitBtnLoading(true);
      try {
        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/category`,
          values,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        toast({
          title: "Category Added Successfully.",
          status: "success",
          duration: 3500,
          isClosable: true,
          position: "top",
        });
        setSubmitBtnLoading(false);
        resetForm();
        navigate(`/category-list`);
      } catch (error) {
        console.log(error);
        setSubmitBtnLoading(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.data.error) {
          toast({
            title: error.response.data.error,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong, please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    },
  });

  const handleImage = async (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();

    reader.readAsDataURL(file);
    reader.onloadend = () => {
      setImagePrev(reader.result);
      setImage(file);
    };

    const formData = new FormData();
    formData.append("image", file);
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/category/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const url = response.data.url;
      formik.setFieldValue("image", url);
      setBtnDisable(false);
      toast({
        title: "You successfully uploaded your category photo",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top"
      });
    } catch (error) {
      setBtnDisable(false);
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Create Category" content="container">
        <Flex justifyContent={"flex-start"} alignItems={"center"} mb={6}>
          <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
          <Breadcrumb fontWeight="medium" fontSize="sm">

            <BreadcrumbItem>
              <Link to="/category-list">Categories</Link>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Add New Category</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
        </Flex>
        <Card mt={2}>
          <CardBody>
            <VStack spacing={4}>
              <form className="col-md-12" onSubmit={formik.handleSubmit}>
                <fieldset>
                  <div className="col-md-12 row">
                    <div className="col-md-12 row mb-3">
                      <FormControl
                        isInvalid={formik.touched.name && formik.errors.name}
                      >
                        <FormLabel htmlFor="name">Name</FormLabel>
                        <Input
                          type="text"
                          id="name"
                          name="name"
                          onBlur={formik.handleBlur}
                          value={formik.values.name}
                          onChange={(e) => {
                            formik.setFieldValue(
                              "name",
                              e.target.value.replace(/[^a-zA-Z0-9\s]/g, "")
                            );
                            formik.setFieldValue(
                              "handle",
                              e.target.value.replace(/\s+/g, "-").toLowerCase()
                            );
                          }}
                        />
                        <FormErrorMessage>
                          {formik.errors.name}
                        </FormErrorMessage>
                      </FormControl>
                    </div>

                    <div className="col-md-12 row mb-3">
                      <FormControl isInvalid={formik.errors.description}>
                        <FormLabel htmlFor="description">Description</FormLabel>
                        <Textarea
                          id="description"
                          name="description"
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          value={formik.values.description}
                        />
                        <FormErrorMessage>
                          {formik.errors.description}
                        </FormErrorMessage>
                      </FormControl>
                    </div>

                    <div className="col-md-12 row mb-3">
                      <FormControl
                        isInvalid={
                          formik.touched.handle && formik.errors.handle
                        }
                      >
                        <FormLabel htmlFor="handle">Handle</FormLabel>
                        <Input
                          type="text"
                          id="handle"
                          name="handle"
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          value={formik.values.handle}
                          onKeyDown={restrictSpace}
                        />
                        <FormErrorMessage>
                          {formik.errors.handle}
                        </FormErrorMessage>
                      </FormControl>
                    </div>
                    <br />
                  </div>

                  <div className="col-md-12 row ">
                    <div className="col-md-12">
                      <FormControl
                        isInvalid={formik.touched.image && formik.errors.image}
                      >
                        <FormLabel htmlFor="image">Image</FormLabel>
                        <Input
                          type="file"
                          id="image"
                          name="image"
                          onChange={(e) => {
                            handleImage(e);
                            setBtnDisable(true);
                          }}
                        />
                        <FormErrorMessage>
                          {formik.errors.image}
                        </FormErrorMessage>
                      </FormControl>
                    </div>
                  </div>

                  <div className="col-md-12 row">
                    <div className="col-md-6 center">
                      {imagePrev && (
                        <Box
                          mb={4}
                          mt={4}
                          w="70%"
                          borderWidth="1px"
                          borderRadius="lg"
                        >
                          <Image src={imagePrev} alt="loading" />
                        </Box>
                      )}
                    </div>
                  </div>
                </fieldset>
                <br />
                <Button
                  colorScheme="telegram"
                  type="submit"
                  w={"100%"}
                  isLoading={submitBtnLoading}
                  isDisabled={btnDisable}
                >
                  Submit
                </Button>
              </form>
            </VStack>
          </CardBody>
        </Card>
      </Layout>
    </Box>
  );
};

export default CategoryAddForm;
