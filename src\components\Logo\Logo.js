import { Image } from '@chakra-ui/react';
import { Link } from 'react-router-dom';
import LogoImage from '../../assets/images/mask/logo.png'; // Import the image

function Logo() {
  return (
    <Link to="/" className="logo-link">
        <div className="logo-wrap">
            <Image src={LogoImage} alt='logo' w={{ base: '90px', sm: '90px', md: '110px' }} h={{ base: '30px', sm: '30px', md: '40px' }} />
        </div>
    </Link>
  )
}

export default Logo;
